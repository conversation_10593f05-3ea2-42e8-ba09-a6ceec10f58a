package com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.typosquatting;

import java.util.Set;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.io.IOException;

import org.apache.commons.text.similarity.LevenshteinDistance;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.BaseCertificateDetector;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 检测Typosquatting攻击的域名证书
 * 通过计算域名与热门域名排行榜中域名的Levenshtein距离来识别可能的域名仿冒
 *
 * Typosquatting是一种网络攻击技术，攻击者注册与知名网站相似的域名，
 * 利用用户输入错误来获取流量或进行网络钓鱼。
 *
 * 重构说明：
 * - 移除了对CSV文件的直接依赖
 * - 改为使用KnowledgeBaseClient从knowledge-base服务获取热门域名数据
 * - 保持原有的检测逻辑不变
 *
 */
@Slf4j
public class TyposquattingDetector extends BaseCertificateDetector {

    private Set<String> topDomainsSet = new HashSet<>();
    private static final double LEVENSHTEIN_THRESHOLD = 0.2; // 20% 相似度阈值

    /**
     * 知识库客户端
     */
    private KnowledgeBaseClient knowledgeBaseClient;

    public TyposquattingDetector() {
        super("Typosquatting Domain Detector");
    }

    /**
     * 初始化检测器
     */
    public void open(Configuration parameters) throws Exception {
        // 初始化知识库客户端
        String knowledgeBaseUrl = parameters.getString("knowledge.base.url", "http://knowledge-base:8080/knowledge-base");
        knowledgeBaseClient = new KnowledgeBaseClient(knowledgeBaseUrl);

        // 从知识库服务加载热门域名数据
        loadTopDomainsFromKnowledgeBase();

        log.info("Initialized {} with {} top domains from knowledge base",
            getClass().getSimpleName(), topDomainsSet.size());
    }

    /**
     * 关闭检测器
     */
    public void close() throws Exception {
        if (knowledgeBaseClient != null) {
            knowledgeBaseClient.close();
        }
    }
    
    /**
     * 从知识库服务加载热门域名数据
     */
    private void loadTopDomainsFromKnowledgeBase() throws IOException {
        try {
            // 从知识库服务获取热门域名数据
            List<Map<String, Object>> popularDomains = knowledgeBaseClient.getPopularDomains(0, 10000);
            topDomainsSet = new HashSet<>();

            for (Map<String, Object> domainInfo : popularDomains) {
                String domain = (String) domainInfo.get("domain");
                if (domain != null && !domain.trim().isEmpty()) {
                    topDomainsSet.add(domain.toLowerCase().trim());
                }
            }

            log.info("成功从知识库加载热门域名数据 {} 个", topDomainsSet.size());

        } catch (Exception e) {
            log.error("从知识库加载热门域名数据失败，使用默认值", e);
            // 使用默认的空集合
            topDomainsSet = new HashSet<>();
            throw new IOException("从知识库加载热门域名数据失败", e);
        }
    }

    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        String fullDomain = cert.getCommonName();
        if (fullDomain == null || fullDomain.isEmpty()) {
            return;
        }

        // 检查域名是否在Tranco排行榜中
        if (topDomainsSet.contains(fullDomain.toLowerCase())) {
            return; // 如果是已知热门域名，直接返回
        }

        // 检查是否是通配符域名
        if (fullDomain.startsWith("*.")) {
            fullDomain = fullDomain.substring(2); // 移除通配符部分
        }

        // 检查是否与Tranco排行榜中的域名相似
        for (String topDomain : topDomainsSet) {
            if (isDomainSuspiciouslySimilar(fullDomain, topDomain)) {
                cert.getLabels().add(CertificateLabel.TYPOSQUATTING);
                log.debug("Detected potential typosquatting domain: {} (similar to {})", fullDomain, topDomain);
                break;
            }
        }
    }

    /**
     * 检查域名是否与热门域名可疑地相似
     * 
     * @param domain 要检查的域名
     * @param topDomain Tranco排行榜中的热门域名
     * @return 如果域名可疑地相似返回true，否则返回false
     */
    private boolean isDomainSuspiciouslySimilar(String domain, String topDomain) {
        // 简单长度检查：如果长度差异太大，直接返回false
        if (Math.abs(domain.length() - topDomain.length()) > topDomain.length() * LEVENSHTEIN_THRESHOLD) {
            return false;
        }

        // 计算Levenshtein距离
        int distance = LevenshteinDistance.getDefaultInstance().apply(domain, topDomain);
        
        // 如果编辑距离在阈值内，则认为可疑
        return distance <= topDomain.length() * LEVENSHTEIN_THRESHOLD;
    }

    /**
     * 获取Tranco排行榜域名集合（用于测试）
     * 
     * @return 包含Tranco排行榜域名的不可变集合
     */
    public Set<String> getTopDomainsSet() {
        return topDomainsSet;
    }
}
