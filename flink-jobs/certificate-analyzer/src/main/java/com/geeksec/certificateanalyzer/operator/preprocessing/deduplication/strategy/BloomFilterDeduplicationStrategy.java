package com.geeksec.certificateanalyzer.operator.preprocessing.deduplication.strategy;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.preprocessing.deduplication.BloomFilterDeduplicator;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;

/**
 * 布隆过滤器去重策略
 * <p>
 * 基于Redis布隆过滤器的轻量级去重实现，适用于高吞吐量场景。
 * 注意：布隆过滤器存在误判可能，但可以显著提升性能。
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class BloomFilterDeduplicationStrategy implements DeduplicationStrategy {
    
    private static final String STRATEGY_NAME = "BloomFilter";
    
    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }
    
    @Override
    public SingleOutputStreamOperator<X509Certificate> deduplicate(DataStream<X509Certificate> certificateStream) {
        return deduplicate(certificateStream, new DeduplicationConfig());
    }
    
    @Override
    public SingleOutputStreamOperator<X509Certificate> deduplicate(
            DataStream<X509Certificate> certificateStream, 
            DeduplicationConfig config) {
        
        log.info("使用布隆过滤器去重策略，并行度: {}", config.getParallelism());
        
        return certificateStream
                .flatMap(new BloomFilterDeduplicator())
                .name("布隆过滤器证书去重")
                .setParallelism(config.getParallelism());
    }
}
