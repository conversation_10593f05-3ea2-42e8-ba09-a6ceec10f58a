package com.geeksec.certificateanalyzer.operator.preprocessing.deduplication.strategy;

import lombok.extern.slf4j.Slf4j;

/**
 * 去重策略工厂
 * <p>
 * 负责创建和管理不同类型的去重策略实例。
 * 支持根据配置动态选择合适的去重策略。
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class DeduplicationStrategyFactory {
    
    /**
     * 去重策略类型枚举
     */
    public enum StrategyType {
        /** 时间窗口去重策略（默认） */
        TIME_WINDOW("TimeWindow"),
        /** 布隆过滤器去重策略 */
        BLOOM_FILTER("BloomFilter");
        
        private final String name;
        
        StrategyType(String name) {
            this.name = name;
        }
        
        public String getName() {
            return name;
        }
        
        public static StrategyType fromName(String name) {
            for (StrategyType type : values()) {
                if (type.getName().equalsIgnoreCase(name)) {
                    return type;
                }
            }
            return TIME_WINDOW; // 默认策略
        }
    }
    
    /**
     * 创建默认去重策略（时间窗口策略）
     * 
     * @return 去重策略实例
     */
    public static DeduplicationStrategy createDefault() {
        return createStrategy(StrategyType.TIME_WINDOW);
    }
    
    /**
     * 根据策略类型创建去重策略
     * 
     * @param strategyType 策略类型
     * @return 去重策略实例
     */
    public static DeduplicationStrategy createStrategy(StrategyType strategyType) {
        switch (strategyType) {
            case TIME_WINDOW:
                log.info("创建时间窗口去重策略");
                return new TimeWindowDeduplicationStrategy();
            case BLOOM_FILTER:
                log.info("创建布隆过滤器去重策略");
                return new BloomFilterDeduplicationStrategy();
            default:
                log.warn("未知的去重策略类型: {}，使用默认时间窗口策略", strategyType);
                return new TimeWindowDeduplicationStrategy();
        }
    }
    
    /**
     * 根据策略名称创建去重策略
     * 
     * @param strategyName 策略名称
     * @return 去重策略实例
     */
    public static DeduplicationStrategy createStrategy(String strategyName) {
        StrategyType strategyType = StrategyType.fromName(strategyName);
        return createStrategy(strategyType);
    }
    
    /**
     * 根据配置创建去重策略
     * 
     * @param strategyType 策略类型
     * @param config 去重配置
     * @return 配置好的去重策略实例
     */
    public static DeduplicationStrategy createStrategy(StrategyType strategyType, 
                                                     DeduplicationStrategy.DeduplicationConfig config) {
        DeduplicationStrategy strategy = createStrategy(strategyType);
        log.info("创建去重策略: {}, 配置: 窗口大小={}ms, 并行度={}, 布隆过滤器={}", 
                strategy.getStrategyName(), 
                config.getWindowSizeMs(), 
                config.getParallelism(), 
                config.isEnableBloomFilter());
        return strategy;
    }
    
    /**
     * 私有构造函数，防止实例化
     */
    private DeduplicationStrategyFactory() {
        throw new UnsupportedOperationException("工具类不能被实例化");
    }
}
