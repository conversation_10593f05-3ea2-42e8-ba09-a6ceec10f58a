package com.geeksec.certificateanalyzer.operator.enrichment;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.util.cert.CertificateNameParser;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书组织信息提取器
 * 负责从证书中提取和验证组织（公司）相关的信息。
 *
 * <AUTHOR>
 */
@Slf4j
public class OrganizationalInfoExtractor {

    private final Map<String, List<String>> countryCompanySuffixMap = new HashMap<>(64);
    private final KnowledgeBaseClient knowledgeBaseClient;

    /**
     * 初始化，从知识库服务加载公司后缀数据。
     */
    public OrganizationalInfoExtractor(KnowledgeBaseClient knowledgeBaseClient) {
        this.knowledgeBaseClient = knowledgeBaseClient;
        loadCompanySuffixesFromKnowledgeBase();
    }

    /**
     * 提取证书中的组织信息。
     *
     * @param certificate 待处理的证书对象
     */
    public void extract(X509Certificate certificate) {
        Map<String, Object> subjectInfo = CertificateNameParser.parse(certificate.getSubject());
        String organizationName = subjectInfo.getOrDefault(CertificateConstants.FIELD_O, "").toString();

        if (!organizationName.isEmpty() && isValidCompany(organizationName)) {
            certificate.setOrganization(organizationName);
        }
    }

    /**
     * 从知识库服务加载公司后缀数据
     */
    private void loadCompanySuffixesFromKnowledgeBase() {
        try {
            // 从知识库服务获取所有公司后缀映射
            Map<String, List<String>> suffixesByCountry = knowledgeBaseClient.getAllCompanySuffixes();

            if (suffixesByCountry != null && !suffixesByCountry.isEmpty()) {
                countryCompanySuffixMap.putAll(suffixesByCountry);
                log.info("成功从知识库加载公司后缀数据，覆盖 {} 个国家", countryCompanySuffixMap.size());
            } else {
                log.warn("从知识库获取的公司后缀数据为空");
            }

        } catch (Exception e) {
            log.error("从知识库加载公司后缀数据失败，使用空映射", e);
            // 保持空映射，避免程序崩溃
        }
    }

    /**
     * 检查名称是否为有效的公司名。
     *
     * @param name 要检查的名称
     * @return 如果是有效的公司名，则返回 true
     */
    private boolean isValidCompany(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        String lowerCaseName = name.toLowerCase();
        for (List<String> suffixList : countryCompanySuffixMap.values()) {
            for (String suffix : suffixList) {
                if (lowerCaseName.contains(suffix.toLowerCase())) {
                    return true;
                }
            }
        }
        return false;
    }
}
