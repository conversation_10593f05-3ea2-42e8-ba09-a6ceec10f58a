package com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.apt;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.BaseCertificateDetector;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * APT28 相关证书检测器
 * 检测条件：
 * 1. 免费证书 (FREE_CERTIFICATE)
 * 2. 最近注册的证书 (RECENTLY_REGISTERED)
 * 3. 非常见顶级域名 (UNHOT_TLD)
 */
@Slf4j
public class APT28Detector extends BaseCertificateDetector {
    public static final String DETECTOR_NAME = "APT28 Detector";
    
    public APT28Detector() {
        super(DETECTOR_NAME);
    }
    
    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }
        
        Set<CertificateLabel> labels = cert.getLabels();
        if (isSuspiciousAPT28(cert)) {
            labels.add(CertificateLabel.APT);
            labels.add(CertificateLabel.THREAT);
            log.debug("Detected APT28 pattern in certificate: {}", cert.getCommonName());
        }
    }
    
    /**
     * 检查证书是否符合APT28的特征
     * 原始检测条件：
     * 1. 免费证书 (FREE_CERTIFICATE)
     * 2. 最近注册的证书 (RECENTLY_REGISTERED)
     * 3. 非常见顶级域名 (UNHOT_TLD)
     */
    private boolean isSuspiciousAPT28(X509Certificate cert) {
        Set<CertificateLabel> labels = cert.getLabels();
        return labels.contains(CertificateLabel.FREE_CERTIFICATE) 
            && labels.contains(CertificateLabel.RECENTLY_REGISTERED)
            && labels.contains(CertificateLabel.UNHOT_TLD);
    }
}
