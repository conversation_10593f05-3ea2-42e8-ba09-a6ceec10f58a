package com.geeksec.certificateanalyzer.model.cert;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.enums.CertificateTrustStatus;
import com.geeksec.certificateanalyzer.model.extension.CertificateExtensionData;
import com.geeksec.certificateanalyzer.model.extension.UncommonOID;

import lombok.Data;

/**
 * X.509证书实体类，对应Doris中的dim_cert表
 *
 * <AUTHOR>
 * @since 2024/06/17
 */
@Data
public class X509Certificate {
    // 基础字段
    private byte[] cert;

    // 主题和颁发者信息
    private Map<String, String> subject = null;

    private Map<String, String> issuer = null;

    // 标识符和哈希
    private String derSha1 = null;

    private String derMd5 = null;

    private String derSha256 = null;

    private String pemMd5 = null;

    private String pemSha256 = null;

    private String pemSha1 = null;

    private String spkiSha256 = null;

    private String correctedAsn1Sha1 = null;

    // 基础信息
    private String version = null;

    private String serialNumber = null;

    private String format = null;

    // 关联ID
    private String issuerId = null;

    private String subjectId = null;

    // 时间相关
    private LocalDateTime notBefore = null;

    private LocalDateTime notAfter = null;

    /**
     * 证书有效期长度，单位：毫秒
     */
    private Long duration = 0L;

    private LocalDateTime importTime = null;

    // 名称和标识
    private String commonName = null;

    private List<String> subjectAltNames = new ArrayList<>();

    private List<String> issuerAltNames = new ArrayList<>();

    // 公钥信息
    private String publicKey = null;

    private String publicKeyAlgorithm = null;

    private String publicKeyLength = null;

    private String publicKeyParameter = null;

    private String publicKeyAlgOid = null;

    private String publicKeyParamOid = null;

    // 签名信息
    private String signature = null;

    private String signatureAlgorithm = null;

    private String signatureAlgName = null;

    private String signatureAlgOid = null;

    // 密钥用途和扩展
    private CertificateExtensionData certificateExtensions;

    /**
     * 密钥用途
     */
    private String keyUsage = null;

    /**
     * 扩展密钥用途
     */
    private List<String> extendedKeyUsage = new ArrayList<>();

    /**
     * 基本约束
     */
    private String basicConstraints = null;

    /**
     * 授权密钥标识符
     */
    private String authorityKeyIdentifier = null;

    /**
     * 主题密钥标识符
     */
    private String subjectKeyIdentifier = null;

    /**
     * CRL分发点
     */
    private List<String> crlDistributionPoints = new ArrayList<>();

    /**
     * 授权信息访问
     */
    private List<String> authorityInfoAccess = new ArrayList<>();

    /**
     * 主题信息访问
     */
    private List<String> subjectInfoAccess = new ArrayList<>();

    /**
     * 证书策略
     */
    private List<String> certPolicies = new ArrayList<>();

    // 扩展信息
    private Map<String, Object> extensions = new java.util.HashMap<>();

    // 证书链信息
    private String parentCertId = "";

    private List<String> certChainIds = new ArrayList<>();

    // 业务分类字段
    /**
     * 证书来源
     */
    private Integer source = 0; // 0: COLLECTED, 1: SYSTEM, 2: USER

    /**
     * 证书的信任状态，由 TrustValidator 根据可信CA库进行评估
     */
    private CertificateTrustStatus trustStatus = CertificateTrustStatus.UNKNOWN;

    private String userType = null;

    private String businessType = null;

    private String caType = null;

    private String industryType = null;

    private String subjectArea = null;

    private String issuerArea = null;

    // 分类字段
    private String userCategory = null;

    private String businessCategory = null;

    private String issuerCategory = null;

    private String industryCategory = null;

    // 状态和标记
    private Boolean isWhitelisted = false;

    private Boolean isBlacklisted = false;

    private Boolean isParsedSuccessfully = false;

    private Boolean isCorrupted = false;

    private Integer certOccurrenceCount = 1;

    private String processingMethod = null;

    private String parseStatus = null;

    // 任务和批次信息
    private String taskId = null;

    private String batchId = null;

    private List<String> userIdList = new ArrayList<>();

    // 评分字段
    private int threatScore = 0;

    private int trustScore = 0;

    private String threatLevel = null;

    private Set<CertificateLabel> labels = new HashSet<>();

    private List<String> forwardChunkHashes = new ArrayList<>();

    private List<String> reverseChunkHashes = new ArrayList<>();

    // 证书内嵌信息
    private List<String> certDomains = new ArrayList<>();

    private List<String> certIps = new ArrayList<>();

    // 特殊字段
    private List<String> uncommonOids = new ArrayList<>();

    private String organization = "";

    // 时间字段
    private LocalDateTime firstSeen = null;

    private LocalDateTime lastSeen = null;

    private LocalDateTime createTime = null;

    private LocalDateTime updateTime = null;

    private String remark = null;

    /**
     * 构造函数
     *
     * @param cert 证书字节数组
     */
    public X509Certificate(byte[] cert) {
        this.cert = cert;
        this.certificateExtensions = new CertificateExtensionData();
        // 初始化时间字段
        LocalDateTime now = LocalDateTime.now();
        this.firstSeen = now;
        this.lastSeen = now;
        this.createTime = now;
        this.updateTime = now;
    }

    /**
     * 获取颁发者信息
     * @return 颁发者信息映射
     */
    public Map<String, String> getIssuer() {
        return this.issuer;
    }

    /**
     * 设置用户分类
     * @param userCategory 用户分类
     */
    public void setUserCategory(String userCategory) {
        this.userCategory = userCategory;
    }

    /**
     * 设置业务分类
     * @param businessCategory 业务分类
     */
    public void setBusinessCategory(String businessCategory) {
        this.businessCategory = businessCategory;
    }

    /**
     * 设置颁发者分类
     * @param issuerCategory 颁发者分类
     */
    public void setIssuerCategory(String issuerCategory) {
        this.issuerCategory = issuerCategory;
    }

    /**
     * 设置行业分类
     * @param industryCategory 行业分类
     */
    public void setIndustryCategory(String industryCategory) {
        this.industryCategory = industryCategory;
    }

    /**
     * 获取通用名称
     * @return 通用名称
     */
    public String getCommonName() {
        return this.commonName;
    }

    /**
     * 获取主题信息
     * @return 主题信息映射
     */
    public Map<String, String> getSubject() {
        return this.subject;
    }

    /**
     * 同步扩展信息字段
     * 将CertificateExtensionData中的字段同步到平铺字段中
     */
    public void syncExtensionFields() {
        if (this.certificateExtensions != null) {
            this.keyUsage = this.certificateExtensions.getKeyUsage();
            this.extendedKeyUsage = this.certificateExtensions.getExtendedKeyUsage() != null
                    ? new ArrayList<>(this.certificateExtensions.getExtendedKeyUsage())
                    : new ArrayList<>();
            this.basicConstraints = this.certificateExtensions.getBasicConstraints();
            this.authorityKeyIdentifier = this.certificateExtensions.getAuthorityKeyIdentifier();
            this.subjectKeyIdentifier = this.certificateExtensions.getSubjectKeyIdentifier();
            this.crlDistributionPoints = this.certificateExtensions.getCrlDistributionPoints() != null
                    ? new ArrayList<>(this.certificateExtensions.getCrlDistributionPoints())
                    : new ArrayList<>();
            this.authorityInfoAccess = this.certificateExtensions.getAuthorityInfoAccess() != null
                    ? new ArrayList<>(this.certificateExtensions.getAuthorityInfoAccess())
                    : new ArrayList<>();
            this.subjectInfoAccess = this.certificateExtensions.getSubjectInfoAccess() != null
                    ? new ArrayList<>(this.certificateExtensions.getSubjectInfoAccess())
                    : new ArrayList<>();
            this.certPolicies = this.certificateExtensions.getCertPolicies() != null
                    ? new ArrayList<>(this.certificateExtensions.getCertPolicies())
                    : new ArrayList<>();
        }
    }

    /**
     * 获取扩展信息
     */
    public java.util.HashMap<String, String> getExtension() {
        java.util.HashMap<String, String> extensionMap = new java.util.HashMap<>();
        if (this.certificateExtensions != null) {
            if (this.keyUsage != null) {
                extensionMap.put("keyUsage", this.keyUsage);
            }
            if (this.basicConstraints != null) {
                extensionMap.put("basicConstraints", this.basicConstraints);
            }
            if (this.authorityKeyIdentifier != null) {
                extensionMap.put("authorityKeyIdentifier", this.authorityKeyIdentifier);
            }
            if (this.subjectKeyIdentifier != null) {
                extensionMap.put("subjectKeyIdentifier", this.subjectKeyIdentifier);
            }
            if (this.extendedKeyUsage != null && !this.extendedKeyUsage.isEmpty()) {
                extensionMap.put("extendedKeyUsage", String.join(",", this.extendedKeyUsage));
            }
        }
        return extensionMap;
    }

    /**
     * 设置扩展信息
     */
    public void setExtension(java.util.HashMap<String, String> extension) {
        if (extension != null) {
            this.keyUsage = extension.get("keyUsage");
            this.basicConstraints = extension.get("basicConstraints");
            this.authorityKeyIdentifier = extension.get("authorityKeyIdentifier");
            this.subjectKeyIdentifier = extension.get("subjectKeyIdentifier");

            String extKeyUsage = extension.get("extendedKeyUsage");
            if (extKeyUsage != null && !extKeyUsage.trim().isEmpty()) {
                this.extendedKeyUsage = java.util.Arrays.asList(extKeyUsage.split(","));
            }
        }
    }

    /**
     * 获取不常见OID列表（字符串格式）
     */
    public List<String> getUncommonOids() {
        return this.uncommonOids;
    }

    /**
     * 获取不常见OID列表（UncommonOID对象格式）
     */
    public List<UncommonOID> getUncommonOIDs() {
        // 简单返回空列表，实际应该从uncommonOids字段转换
        return new ArrayList<>();
    }

    /**
     * 设置不常见OID列表
     */
    public void setUncommonOIDs(List<com.geeksec.certificateanalyzer.model.extension.UncommonOID> uncommonOids) {
        // 简单转换为字符串列表
        if (uncommonOids != null) {
            this.uncommonOids = uncommonOids.stream()
                    .map(oid -> oid.getOID())
                    .collect(java.util.stream.Collectors.toList());
        }
    }

    /**
     * 获取序列号
     * @return 序列号
     */
    public String getSerialNumber() {
        return this.serialNumber;
    }

    /**
     * 获取标签集合
     * @return 标签集合
     */
    public Set<CertificateLabel> getLabels() {
        return this.labels;
    }

    /**
     * 获取前向块哈希列表
     * @return 前向块哈希列表
     */
    public List<String> getForwardChunkHashes() {
        return this.forwardChunkHashes;
    }

    /**
     * 设置证书是否格式良好
     * @param wellFormed 是否格式良好
     */
    public void setWellFormed(boolean wellFormed) {
        // 可以根据需要设置相关字段
        this.isParsedSuccessfully = wellFormed;
        this.isCorrupted = !wellFormed;
    }

    /**
     * 设置威胁评分
     */
    public void setThreatScore(int threatScore) {
        // 根据黑名单状态设置威胁分数
        if (threatScore > 0) {
            this.threatScore = Math.max(this.threatScore, 60);
        }
    }

    /**
     * 设置可信评分
     */
    public void setTrustScore(int trustScore) {
        if (trustScore > 0) {
            this.trustScore = Math.max(this.trustScore, 80);
        }
    }
}
