package com.geeksec.certificateanalyzer.operator.analysis.detection.service;

import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.CertificateDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.apt.APT28Detector;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.apt.APT29Detector;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.apt.PatchWorkDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.TorDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.OpenSSLSelfSignDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.BlockedCertificateDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.botnet.DanaBotDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.botnet.QuakbotDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.botnet.StealcDetector;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.typosquatting.TyposquattingDomainDetector;

import lombok.extern.slf4j.Slf4j;
import java.util.*;

/**
 * 威胁检测器工厂类，负责创建和管理所有威胁检测器实例
 */
@Slf4j
public class ThreatDetectorFactory {
    private static ThreatDetectorFactory instance;
    private final List<CertificateDetector> detectors;
    
    // BotnetCertificateDetector will be initialized with null dependencies
    // In a production environment, these should be properly initialized with actual models and data

    private ThreatDetectorFactory() {
        detectors = new ArrayList<>();
        initializeDetectors();
    }

    public static synchronized ThreatDetectorFactory getInstance() {
        if (instance == null) {
            instance = new ThreatDetectorFactory();
        }
        return instance;
    }



    private void initializeDetectors() {
        // 添加所有检测器
        // APT相关检测器
        detectors.add(new APT28Detector());
        detectors.add(new APT29Detector());
        detectors.add(new PatchWorkDetector());
        
        // 其他威胁检测器
        detectors.add(new TorDetector());
        
        // OpenSSL自签名证书检测器
        detectors.add(new OpenSSLSelfSignDetector());
        
        // 添加Botnet相关检测器
        try {
            // 注意：TyposquattingDomainDetector需要alexaTop10kMap参数
            // 这里传入空Map，实际项目中应该提供真实的Alexa Top 10K域名数据
            detectors.add(new TyposquattingDomainDetector(new HashMap<>()));
            log.info("Initialized TyposquattingDetector");
            
            // 添加其他Botnet检测器
            detectors.add(new DanaBotDetector());
            detectors.add(new QuakbotDetector());
            detectors.add(new StealcDetector());
            log.info("Initialized all botnet detectors");
            
        } catch (Exception e) {
            log.error("Error initializing botnet detectors: {}", e.getMessage(), e);
        }
        
        detectors.add(new BlockedCertificateDetector());
    }

    public List<CertificateDetector> getAllDetectors() {
        return new ArrayList<>(detectors);
    }
}
