package com.geeksec.certificateanalyzer.pipeline;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SideOutputDataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.AttributeAnalyzer;
import com.geeksec.certificateanalyzer.operator.analysis.OidAnalyzer;
import com.geeksec.certificateanalyzer.operator.analysis.SecurityFeatureAnalyzer;
import com.geeksec.certificateanalyzer.operator.analysis.ThreatDetectionAnalyzer;
import com.geeksec.certificateanalyzer.operator.analysis.scoring.CertificateRiskScorer;
import com.geeksec.certificateanalyzer.operator.common.outputtags.PreprocessingOutputTags;
import com.geeksec.certificateanalyzer.operator.enrichment.MetadataExtractor;
import com.geeksec.certificateanalyzer.operator.preprocessing.deduplication.DeduplicationOperator;
import com.geeksec.certificateanalyzer.operator.validation.TrustValidator;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 证书处理流水线
 * 负责构建统一的、基于信任状态的证书分析数据流处理管道
 *
 * <AUTHOR>
 */
@Slf4j
public class CertificateProcessingPipeline {

    /**
     * 构建证书分析流水线
     *
     * @param certificateStream 输入的证书数据流
     * @param parameterTool 配置参数
     * @return 流水线处理结果
     */
    public static PipelineResult build(DataStream<X509Certificate> certificateStream, ParameterTool parameterTool) {
        log.info("构建证书分析流水线");

        // 1. 预处理 - 将流分为正常流和错误流
        SingleOutputStreamOperator<X509Certificate> preprocessedStream = addPreprocessing(certificateStream);

        // 2. 对正常证书应用统一的验证和分析流程
        DataStream<X509Certificate> processedStream = addUnifiedCertificateProcessing(preprocessedStream);

        // 3. 获取错误流
        SideOutputDataStream<X509Certificate> errorStream = preprocessedStream
                .getSideOutput(PreprocessingOutputTags.CORRUPTED_CERTIFICATE);

        return new PipelineResult(processedStream, errorStream, parameterTool);
    }

    /**
     * 添加预处理步骤
     */
    private static SingleOutputStreamOperator<X509Certificate> addPreprocessing(DataStream<X509Certificate> certificateStream) {
        log.info("添加证书预处理步骤");

        return certificateStream
                .process(new ProcessFunction<X509Certificate, X509Certificate>() {
                    @Override
                    public void processElement(X509Certificate certificate, Context context, Collector<X509Certificate> out) throws Exception {
                        try {
                            // 基本验证
                            if (certificate != null && certificate.getDerSha1() != null && !certificate.getDerSha1().isEmpty()) {
                                // 输出到正常流
                                context.output(PreprocessingOutputTags.NORMAL_CERTIFICATE, certificate);
                                out.collect(certificate);
                            } else {
                                // 输出到错误流
                                context.output(PreprocessingOutputTags.CORRUPTED_CERTIFICATE, certificate);
                                log.warn("发现无效证书，已路由到错误处理流程");
                            }
                        } catch (Exception e) {
                            log.error("证书预处理失败", e);
                            context.output(PreprocessingOutputTags.CORRUPTED_CERTIFICATE, certificate);
                        }
                    }
                })
                .name("证书预处理")
                .setParallelism(4);
    }

    /**
     * 添加统一的证书处理流程
     */
    private static DataStream<X509Certificate> addUnifiedCertificateProcessing(SingleOutputStreamOperator<X509Certificate> preprocessedStream) {
        log.info("添加统一证书处理流程");

        // 获取正常证书流
        DataStream<X509Certificate> mainStream = preprocessedStream.getSideOutput(PreprocessingOutputTags.NORMAL_CERTIFICATE);

        // 1. 证书去重
        SingleOutputStreamOperator<X509Certificate> dedupStream = DeduplicationOperator.deduplicate(mainStream);

        // 2. 证书信任状态验证
        SingleOutputStreamOperator<X509Certificate> validatedStream = dedupStream
                .map(new TrustValidator())
                .name("证书信任状态验证")
                .setParallelism(4);

        // 3. 证书地理与组织信息提取
        SingleOutputStreamOperator<X509Certificate> enrichedStream = validatedStream
                .map(new MetadataExtractor())
                .name("证书地理与组织信息提取")
                .setParallelism(4);

        // 4. 证书属性分析
        SingleOutputStreamOperator<X509Certificate> attributeAnalyzedStream = enrichedStream
                .map(new AttributeAnalyzer())
                .name("证书属性分析")
                .setParallelism(4);

        // 5. 证书安全特征分析
        SingleOutputStreamOperator<X509Certificate> securityAnalyzedStream = attributeAnalyzedStream
                .map(new SecurityFeatureAnalyzer())
                .name("证书安全特征分析")
                .setParallelism(4);

        // 6. 证书OID分析
        SingleOutputStreamOperator<X509Certificate> oidAnalyzedStream = securityAnalyzedStream
                .map(new OidAnalyzer())
                .name("证书OID分析")
                .setParallelism(4);

        // 7. 威胁检测分析
        SingleOutputStreamOperator<X509Certificate> threatAnalyzedStream = oidAnalyzedStream
                .map(new ThreatDetectionAnalyzer())
                .name("威胁检测分析")
                .setParallelism(4);

        // 8. 风险评分
        SingleOutputStreamOperator<X509Certificate> scoredStream = threatAnalyzedStream
                .map(new CertificateRiskScorer())
                .name("证书风险评分")
                .setParallelism(4);

        log.info("证书处理流水线构建完成");
        return scoredStream;
    }

    /**
     * 流水线处理结果
     */
    @Data
    public static class PipelineResult {
        private final DataStream<X509Certificate> processedStream;
        private final SideOutputDataStream<X509Certificate> errorStream;
        private final ParameterTool parameterTool;

        public PipelineResult(DataStream<X509Certificate> processedStream,
                              SideOutputDataStream<X509Certificate> errorStream, 
                              ParameterTool parameterTool) {
            this.processedStream = processedStream;
            this.errorStream = errorStream;
            this.parameterTool = parameterTool;
        }
    }
}
