package com.geeksec.certificateanalyzer.operator.analysis;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书安全性分析器
 * 负责分析证书的安全特征并生成相应的安全标签
 * 包括：证书类型识别、安全风险检测、合规性检查等
 *
 * <AUTHOR>
 * @Date 2022/10/20
 * @Modified hufengkai - 业务语义优化和知识库集成
 * @Date 2024/12/19
 */
@Slf4j
public class SecurityFeatureAnalyzer extends RichMapFunction<X509Certificate, X509Certificate> {

    /** MD5 with RSA 签名算法常量 */
    private static final String MD5_WITH_RSA = "MD5withRSAEncryption";

    /** SHA1 with RSA 签名算法常量 */
    private static final String SHA1_WITH_RSA = "SHA1withRSAEncryption";

    /** 证书版本V3常量 */
    private static final String CERT_VERSION_V3 = "v3";

    /** 密钥用途数量阈值，超过此值认为是服务器证书用作客户端 */
    private static final int KEY_USAGE_THRESHOLD = 9;

    /** 免费证书颁发机构列表 */
    private List<String> freeCertificateAuthorities = new ArrayList<>();

    /** 知识库客户端 */
    private KnowledgeBaseClient knowledgeBaseClient;

    @Override
    public void open(Configuration parameters) throws Exception {
        log.info("证书安全特征分析器初始化开始");

        // 初始化知识库客户端
        String knowledgeBaseUrl = parameters.getString("knowledge.base.url", "http://knowledge-base:8080/knowledge-base");
        knowledgeBaseClient = new KnowledgeBaseClient(knowledgeBaseUrl);

        loadSecurityAnalysisData();
    }

    @Override
    public void close() throws Exception {
        if (knowledgeBaseClient != null) {
            knowledgeBaseClient.close();
        }
        super.close();
        log.info("证书安全特征分析器关闭");
    }

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("分析证书安全特征，证书ID: {}", certificate.getDerSha1());

        // 执行安全特征分析并生成标签
        evaluateSecurityLabels(certificate);

        return certificate;
    }

    /**
     * 加载安全分析所需的依赖数据
     */
    private void loadSecurityAnalysisData() throws IOException {
        try {
            // 从知识库服务加载免费CA列表
            freeCertificateAuthorities = knowledgeBaseClient.getFreeCertificateAuthorities();
            log.info("成功从知识库加载免费CA列表 {} 个", freeCertificateAuthorities.size());

        } catch (Exception e) {
            log.error("从知识库加载安全分析数据失败，使用默认配置", e);
            // 使用默认的免费CA列表
            freeCertificateAuthorities = Arrays.asList(
                    "ZeroSSL.com", "zerossl",
                    "LetsEncrypt.org", "letsencrypt",
                    "LetsEncrypt.org_test", "letsencrypt_test", "letsencrypttest",
                    "BuyPass.com", "buypass",
                    "BuyPass.com_test", "buypass_test", "buypasstest",
                    "SSL.com", "sslcom",
                    "Google.com", "google",
                    "Google.com_test", "googletest", "google_test", "Cloudflare",
                    "SSL For Free", "WoTrus", "TrustAsia", "TrustAsia", "Microsoft Azure",
                    "GlobalSign SSL/TLS Certificates", "Comodo CA"
            );
        }
    }

    /**
     * 分析证书的安全性并生成相应标签
     */
    private void evaluateSecurityLabels(X509Certificate certificate) throws Exception {
        Set<CertificateLabel> securityLabels = certificate.getLabels();
        if (securityLabels == null) {
            securityLabels = new HashSet<>();
        }

        // 检查Windows信任根证书标签
        checkWindowsTrustedRootCertificates(certificate, securityLabels);
        
        // 检查签名算法安全标签
        checkSignatureAlgorithmSecurity(certificate, securityLabels);
        
        // 评估证书版本合规性标签
        evaluateCertificateVersionLabel(certificate, securityLabels);
        
        // 分析密钥用途标签
        evaluateKeyUsageLabels(certificate, securityLabels);
        
        // 检查证书费用类型（付费/免费）
        analyzeCertificateCostType(certificate, securityLabels);
        
        certificate.setLabels(securityLabels);
    }



    /**
     * 检查Windows信任根证书
     */
    private void checkWindowsTrustedRootCertificates(X509Certificate certificate, Set<CertificateLabel> securityLabels) {
        String certificateSha1 = certificate.getDerSha1();
        List<String> windowsTrustedRoots = Arrays.asList(
                "06f1aa330b927b753a40e68cdf22e34bcbef3352",
                "31f9fc8ba3805986b721ea7295c65b3a44534274",
                "0119e81be9a14cd8e22f40ac118c687ecba3f4d8",
                "0563b8630d62d75abbc8ab1e4bdfb5a899b24d43"
        );

        if (certificateSha1 != null && windowsTrustedRoots.contains(certificateSha1.toLowerCase())) {
            securityLabels.add(CertificateLabel.WINDOWS_TRUST);
        }
    }

    /**
     * 检查签名算法安全性
     */
    private void checkSignatureAlgorithmSecurity(X509Certificate certificate, Set<CertificateLabel> securityLabels) {
        String signatureAlgorithm = certificate.getSignatureAlgName();

        if (signatureAlgorithm == null) {
            return;
        }

        if (MD5_WITH_RSA.equalsIgnoreCase(signatureAlgorithm)) {
            securityLabels.add(CertificateLabel.INSECURE_PUBKEY);
        }

        if (MD5_WITH_RSA.equalsIgnoreCase(signatureAlgorithm) ||
                SHA1_WITH_RSA.equalsIgnoreCase(signatureAlgorithm)) {
            securityLabels.add(CertificateLabel.WEAK_ALGORITHM);
        }
    }

    /**
     * 评估证书版本合规性标签
     */
    private void evaluateCertificateVersionLabel(X509Certificate certificate, Set<CertificateLabel> securityLabels) {
        String version = certificate.getVersion();
        if (!CERT_VERSION_V3.equals(version)) {
            securityLabels.add(CertificateLabel.INSECURE_VERSION);
        }
    }

    /**
     * 评估密钥用途标签
     */
    private void evaluateKeyUsageLabels(X509Certificate certificate, Set<CertificateLabel> securityLabels) {
        String keyUsage = certificate.getKeyUsage();
        if (keyUsage != null && !keyUsage.isEmpty()) {
            String[] keyUsageItems = keyUsage.split(", ");

            if (keyUsageItems.length >= KEY_USAGE_THRESHOLD) {
                securityLabels.add(CertificateLabel.SERVER_CERT_AS_CLIENT);
            }
        }
    }

    /**
     * 分析证书费用类型
     */
    private void analyzeCertificateCostType(X509Certificate certificate, Set<CertificateLabel> securityLabels) {
        String issuerName = certificate.getIssuer().toString().toLowerCase();

        for (String freeCa : freeCertificateAuthorities) {
            if (issuerName.contains(freeCa.toLowerCase())) {
                securityLabels.add(CertificateLabel.FREE_CERTIFICATE);
                break;
            }
        }
    }
}