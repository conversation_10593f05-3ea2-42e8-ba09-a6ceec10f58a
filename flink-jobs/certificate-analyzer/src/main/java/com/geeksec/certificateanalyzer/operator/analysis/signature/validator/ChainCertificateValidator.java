package com.geeksec.certificateanalyzer.operator.analysis.signature.validator;

import com.geeksec.certificateanalyzer.operator.analysis.signature.context.CertificateValidationContext;
import com.geeksec.certificateanalyzer.operator.analysis.signature.result.ValidationResult;

/**
 * 证书链验证器接口
 * 专门用于需要证书链验证的验证器
 * 
 * <AUTHOR>
 */
public interface ChainCertificateValidator extends CertificateValidator {
    
    /**
     * 验证证书链
     * 
     * @param context 验证上下文
     * @return 验证结果
     */
    ValidationResult validateChain(CertificateValidationContext context);
    
    /**
     * 默认实现，调用证书链验证
     */
    @Override
    default ValidationResult validate(CertificateValidationContext context) {
        return validate<PERSON>hain(context);
    }
}
