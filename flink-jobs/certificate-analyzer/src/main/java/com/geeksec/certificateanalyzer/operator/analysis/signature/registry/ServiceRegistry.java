package com.geeksec.certificateanalyzer.operator.analysis.signature.registry;

import com.geeksec.certificateanalyzer.operator.analysis.signature.factory.CertificateValidatorFactory;
import com.geeksec.certificateanalyzer.operator.analysis.signature.service.*;
import com.geeksec.certificateanalyzer.repository.CertificateRepository;
import com.geeksec.certificateanalyzer.repository.doris.DorisCertificateRepository;
import com.geeksec.certificateanalyzer.repository.doris.DorisConnectionProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 服务注册器
 * 在非Spring环境下提供服务实例管理
 * 
 * <AUTHOR>
 */
@Slf4j
public class ServiceRegistry {
    
    private static final ServiceRegistry INSTANCE = new ServiceRegistry();
    private final ConcurrentMap<Class<?>, Object> services = new ConcurrentHashMap<>();
    private volatile boolean initialized = false;
    
    private ServiceRegistry() {
        // 私有构造函数
    }
    
    public static ServiceRegistry getInstance() {
        return INSTANCE;
    }
    
    /**
     * 初始化所有服务
     */
    public synchronized void initialize() {
        if (initialized) {
            log.debug("服务注册器已经初始化，跳过重复初始化");
            return;
        }
        
        try {
            log.info("开始初始化服务注册器");
            
            // 1. 创建基础服务
            CertificateRepository certificateRepository = createCertificateRepository();
            registerService(CertificateRepository.class, certificateRepository);
            
            // 2. 创建业务服务
            TrustedCertificateService trustedCertificateService = 
                new TrustedCertificateService(certificateRepository);
            registerService(TrustedCertificateService.class, trustedCertificateService);
            
            SignatureVerificationService signatureVerificationService = 
                new SignatureVerificationService(certificateRepository);
            registerService(SignatureVerificationService.class, signatureVerificationService);
            
            // 3. 创建组合服务
            CertificateChainValidationService chainValidationService =
                new CertificateChainValidationService(
                    trustedCertificateService,
                    signatureVerificationService);
            registerService(CertificateChainValidationService.class, chainValidationService);

            // 4. 创建工厂
            CertificateValidatorFactory validatorFactory =
                new com.geeksec.certificateanalyzer.pipeline.analysis.signature.factory.DefaultCertificateValidatorFactory(
                    chainValidationService);
            registerService(CertificateValidatorFactory.class, validatorFactory);
            
            initialized = true;
            log.info("服务注册器初始化完成，注册了 {} 个服务", services.size());
            
        } catch (Exception e) {
            log.error("服务注册器初始化失败", e);
            throw new RuntimeException("服务注册器初始化失败", e);
        }
    }
    
    /**
     * 获取服务实例
     */
    @SuppressWarnings("unchecked")
    public <T> T getService(Class<T> serviceClass) {
        if (!initialized) {
            initialize();
        }
        
        T service = (T) services.get(serviceClass);
        if (service == null) {
            throw new IllegalArgumentException("未找到服务: " + serviceClass.getName());
        }
        return service;
    }
    
    /**
     * 注册服务
     */
    public <T> void registerService(Class<T> serviceClass, T serviceInstance) {
        if (serviceInstance == null) {
            throw new IllegalArgumentException("服务实例不能为null");
        }
        
        services.put(serviceClass, serviceInstance);
        log.debug("注册服务: {} -> {}", serviceClass.getSimpleName(), serviceInstance.getClass().getSimpleName());
    }
    
    /**
     * 检查服务是否已注册
     */
    public boolean hasService(Class<?> serviceClass) {
        return services.containsKey(serviceClass);
    }
    
    /**
     * 清理所有服务
     */
    public synchronized void clear() {
        log.info("清理服务注册器，当前注册服务数: {}", services.size());
        services.clear();
        initialized = false;
    }
    
    /**
     * 创建证书仓库
     */
    private CertificateRepository createCertificateRepository() {
        try {
            DorisConnectionProvider connectionProvider = new DorisConnectionProvider();
            return new DorisCertificateRepository(connectionProvider);
        } catch (Exception e) {
            log.error("创建证书仓库失败", e);
            throw new RuntimeException("创建证书仓库失败", e);
        }
    }
    
    /**
     * 获取已注册的服务数量
     */
    public int getServiceCount() {
        return services.size();
    }
    
    /**
     * 检查是否已初始化
     */
    public boolean isInitialized() {
        return initialized;
    }
}
