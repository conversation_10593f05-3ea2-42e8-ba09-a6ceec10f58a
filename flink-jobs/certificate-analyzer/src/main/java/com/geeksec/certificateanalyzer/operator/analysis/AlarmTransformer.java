package com.geeksec.certificateanalyzer.operator.analysis;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 告警平铺映射函数
 *
 *
 * <AUTHOR>
 * @Date 2022/12/30
 * @Modified hufengkai - 重构为使用知识库服务
 * @Date 2024/12/22
 */
@Slf4j
public class AlarmTransformer implements MapFunction<X509Certificate, Alarm> {

    /**
     * 告警信息映射表，从知识库服务获取
     */
    public static Map<String, Map<String, String>> alarmInfoMap = new HashMap<>();

    /**
     * 知识库客户端
     */
    private KnowledgeBaseClient knowledgeBaseClient;

    @Override
    public void open(Configuration parameters) throws Exception {
        log.info("告警平铺映射函数初始化开始");

        // 初始化知识库客户端
        String knowledgeBaseUrl = parameters.getString("knowledge.base.url", "http://knowledge-base:8080/knowledge-base");
        knowledgeBaseClient = new KnowledgeBaseClient(knowledgeBaseUrl);

        // 从知识库服务加载告警配置数据
        loadAlarmDataFromKnowledgeBase();

        log.info("告警平铺映射函数初始化完成，告警配置: {} 条", alarmInfoMap.size());
        super.open(parameters);
    }

    /**
     * 从知识库服务加载告警配置数据
     */
    private void loadAlarmDataFromKnowledgeBase() throws IOException {
        try {
            // TODO: 待knowledge-base服务补充告警配置API后，替换以下临时实现
            // 临时使用空映射，避免空指针异常
            alarmInfoMap = new HashMap<>();

            log.warn("告警配置数据暂时使用空映射，待knowledge-base服务补充告警配置API接口");

        } catch (Exception e) {
            log.error("从知识库加载告警配置数据失败，使用默认值", e);
            // 使用默认的空映射
            alarmInfoMap = new HashMap<>();
            throw new IOException("从知识库加载告警配置数据失败", e);
        }
    }

    @Override
    public void flatMap(Row row, Collector<JSONObject> collector) throws Exception {
        String alarmType = row.getFieldAs(0);
        JSONObject sendJson;
        switch (alarmType) {
            case "APT证书碰撞":
            case "威胁证书碰撞":
            case "失陷IP关联证书":
            case "恶意域名关联证书":
            case "APT证书上线":
            case "翻墙行为":
            case "TOR网络访问":
            case "C2证书请求":
            case "非法挖矿请求":
                sendJson = KnowledgeBaseAlarmSink.getUseCertAlarmJson(row);
                collector.collect(sendJson);
                break;
            default:
                log.error("告警类型出错");
                break;
        }
    }

    @Override
    public void close() throws Exception {
        // 关闭知识库客户端
        if (knowledgeBaseClient != null) {
            knowledgeBaseClient.close();
        }
        super.close();
    }
}
