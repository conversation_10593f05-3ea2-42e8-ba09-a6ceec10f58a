package com.geeksec.certificateanalyzer.operator.common.outputtags;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import org.apache.flink.types.Row;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * 数据输出阶段输出标签
 * 用于证书数据输出到各种存储系统的流控制
 *
 * <AUTHOR>
 */
public final class SinkOutputTags {
    private SinkOutputTags() {
        // 防止实例化
    }

    /** MinIO存储输出标签 - 存储原始证书对象 */
    public static final OutputTag<X509Certificate> MINIO_STORAGE =
        new OutputTag<>("minio-storage", TypeInformation.of(X509Certificate.class));

    /** Doris存储输出标签 - 存储证书对象，统一写入dim_cert表 */
    public static final OutputTag<X509Certificate> DORIS_STORAGE =
        new OutputTag<>("doris-storage", TypeInformation.of(X509Certificate.class));

    /** Nebula图数据库输出标签 - 存储Row格式数据 */
    public static final OutputTag<Row> NEBULA_STORAGE =
        new OutputTag<>("nebula-storage", TypeInformation.of(Row.class));
}
