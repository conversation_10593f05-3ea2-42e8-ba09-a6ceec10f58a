package com.geeksec.certificateanalyzer.util.math;

import com.geeksec.certificateanalyzer.config.CertificateConstants;
import java.util.HashMap;
import java.util.Map;

/**
 * 熵计算工具类
 * 专门负责计算字符串的信息熵
 * 
 * <AUTHOR>
 */
public final class EntropyCalculator {
    
    /**
     * 私有构造函数，防止实例化
     */
    private EntropyCalculator() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
    
    /**
     * 计算字符串的信息熵
     * 
     * @param str 输入字符串
     * @return 信息熵值
     */
    public static double calculateEntropy(String str) {
        if (str == null || str.isEmpty()) {
            return 0.0;
        }
        
        Map<Character, Integer> charFrequencyMap = buildCharacterFrequencyMap(str);
        int totalLength = str.length();
        
        double entropy = 0.0;
        for (Map.Entry<Character, Integer> entry : charFrequencyMap.entrySet()) {
            double probability = (double) entry.getValue() / totalLength;
            entropy -= probability * (Math.log(probability) / Math.log(2));
        }
        
        return entropy;
    }
    
    /**
     * 构建字符频率映射
     * 
     * @param str 输入字符串
     * @return 字符频率映射
     */
    private static Map<Character, Integer> buildCharacterFrequencyMap(String str) {
        Map<Character, Integer> charFrequencyMap = new HashMap<>();
        
        for (char c : str.toCharArray()) {
            charFrequencyMap.merge(c, 1, Integer::sum);
        }
        
        return charFrequencyMap;
    }
}
