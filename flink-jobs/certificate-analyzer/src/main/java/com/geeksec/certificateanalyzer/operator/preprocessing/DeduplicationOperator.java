package com.geeksec.certificateanalyzer.operator.preprocessing;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;

import java.util.HashMap;
import java.util.Map;

/**
 * 证书去重算子
 * <p>
 * 负责对证书数据流进行去重处理，基于证书的SHA1指纹进行去重。
 * 使用滑动窗口机制，在指定时间窗口内对相同的证书进行去重。
 * <p>
 * 去重策略：
 * - 基于证书的DER SHA1指纹进行去重
 * - 使用时间窗口避免内存无限增长
 * - 保留最新的证书实例
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class DeduplicationOperator {

    /**
     * 默认去重窗口大小（毫秒）
     */
    private static final long DEFAULT_WINDOW_SIZE_MS = 5000L;

    /**
     * 对证书数据流进行去重处理
     *
     * @param certificateStream 输入的证书数据流
     * @return 去重后的证书数据流
     */
    public static SingleOutputStreamOperator<X509Certificate> deduplicate(
            DataStream<X509Certificate> certificateStream) {
        return deduplicate(certificateStream, DEFAULT_WINDOW_SIZE_MS);
    }

    /**
     * 对证书数据流进行去重处理（指定窗口大小）
     *
     * @param certificateStream 输入的证书数据流
     * @param windowSizeMs     去重窗口大小（毫秒）
     * @return 去重后的证书数据流
     */
    public static SingleOutputStreamOperator<X509Certificate> deduplicate(
            DataStream<X509Certificate> certificateStream, long windowSizeMs) {
        
        log.info("开始证书去重处理，窗口大小: {}ms", windowSizeMs);

        return certificateStream
                // 分配时间戳和水印
                .assignTimestampsAndWatermarks(WatermarkStrategy.forMonotonousTimestamps())
                .name("证书去重时间戳分配")
                // 按证书SHA1指纹分组
                .keyBy(new CertificateKeySelector())
                // 设置滑动窗口
                .window(TumblingProcessingTimeWindows.of(Time.milliseconds(windowSizeMs)))
                // 去重处理
                .process(new CertificateDeduplicationFunction())
                .name("证书去重处理")
                .setParallelism(4);
    }

    /**
     * 证书键选择器
     * <p>
     * 基于证书的SHA1指纹进行分组，相同指纹的证书会被分到同一个组中进行去重。
     */
    private static class CertificateKeySelector implements KeySelector<X509Certificate, String> {
        @Override
        public String getKey(X509Certificate certificate) throws Exception {
            // 使用SHA1指纹作为去重键
            String sha1 = certificate.getDerSha1();
            return sha1 != null ? sha1 : "unknown";
        }
    }

    /**
     * 证书去重处理函数
     * <p>
     * 在时间窗口内对相同SHA1指纹的证书进行去重，保留最新的证书实例。
     */
    private static class CertificateDeduplicationFunction 
            extends ProcessWindowFunction<X509Certificate, X509Certificate, String, TimeWindow> {

        @Override
        public void process(String key, Context context, 
                          Iterable<X509Certificate> elements, 
                          Collector<X509Certificate> out) throws Exception {
            
            Map<String, X509Certificate> certificateMap = new HashMap<>();
            int totalCount = 0;
            
            // 遍历窗口内的所有证书
            for (X509Certificate cert : elements) {
                totalCount++;
                String sha1 = cert.getDerSha1();
                
                if (sha1 != null) {
                    // 如果已存在相同SHA1的证书，比较时间戳，保留最新的
                    X509Certificate existing = certificateMap.get(sha1);
                    if (existing == null || isNewer(cert, existing)) {
                        certificateMap.put(sha1, cert);
                    }
                } else {
                    // 对于没有SHA1的证书，直接输出（通常是解析失败的证书）
                    out.collect(cert);
                }
            }
            
            // 输出去重后的证书
            for (X509Certificate cert : certificateMap.values()) {
                out.collect(cert);
            }
            
            int deduplicatedCount = certificateMap.size();
            if (totalCount > deduplicatedCount) {
                log.debug("窗口去重完成，原始数量: {}, 去重后数量: {}, 去重率: {:.2f}%", 
                         totalCount, deduplicatedCount, 
                         (1.0 - (double)deduplicatedCount / totalCount) * 100);
            }
        }

        /**
         * 判断证书是否更新
         * <p>
         * 比较两个证书的导入时间，返回较新的证书。
         *
         * @param cert1 证书1
         * @param cert2 证书2
         * @return 如果cert1更新则返回true
         */
        private boolean isNewer(X509Certificate cert1, X509Certificate cert2) {
            if (cert1.getImportTime() == null) return false;
            if (cert2.getImportTime() == null) return true;
            return cert1.getImportTime().isAfter(cert2.getImportTime());
        }
    }

    /**
     * 私有构造函数，防止实例化
     */
    private DeduplicationOperator() {
        throw new UnsupportedOperationException("工具类不能被实例化");
    }
}
