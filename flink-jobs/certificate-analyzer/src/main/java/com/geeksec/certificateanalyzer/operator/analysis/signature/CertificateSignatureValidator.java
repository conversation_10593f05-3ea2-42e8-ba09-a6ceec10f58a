package com.geeksec.certificateanalyzer.operator.analysis.signature;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.signature.factory.CertificateValidatorFactory;
import com.geeksec.certificateanalyzer.operator.analysis.signature.factory.DefaultCertificateValidatorFactory;
import com.geeksec.certificateanalyzer.operator.analysis.signature.validator.CertificateValidator;
import com.geeksec.certificateanalyzer.sink.minio.CertificateStorageService;
import com.geeksec.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书签名验证器
 * 负责协调证书签名验证流程，决定证书是否需要进行签名验证
 *
 * <AUTHOR>
 */
@Slf4j
public class CertificateSignatureValidator extends RichMapFunction<X509Certificate, X509Certificate> {

    /** 验证类型映射 */
    public static Map<String, String> validationTypeMap = new HashMap<>();

    /** 知识库客户端 */
    private KnowledgeBaseClient knowledgeBaseClient;

    /** 证书验证器工厂 */
    private CertificateValidatorFactory validatorFactory;

    /** 证书存储服务 */
    private CertificateStorageService storageService;

    /** 需要验证的证书SHA1列表 */
    private Set<String> certificatesToValidate = new HashSet<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化知识库客户端
        knowledgeBaseClient = new KnowledgeBaseClient();
        
        // 初始化验证器工厂
        validatorFactory = new DefaultCertificateValidatorFactory();
        
        // 初始化存储服务
        storageService = new CertificateStorageService();
        
        // 加载验证配置
        loadValidationConfiguration();
    }

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("开始证书签名验证，证书ID: {}", certificate.getDerSha1());

        try {
            // 检查是否需要验证
            if (shouldValidateCertificate(certificate)) {
                // 执行签名验证
                performSignatureValidation(certificate);
            } else {
                log.debug("证书无需验证，跳过签名验证: {}", certificate.getDerSha1());
            }
        } catch (Exception e) {
            log.error("证书签名验证失败: {}", certificate.getDerSha1(), e);
            // 添加验证失败标签
            addValidationFailedLabel(certificate);
        }

        return certificate;
    }

    /**
     * 加载验证配置
     */
    private void loadValidationConfiguration() throws IOException {
        try {
            // 从知识库加载需要验证的证书列表
            certificatesToValidate = knowledgeBaseClient.getCertificatesToValidate();
            log.info("成功从知识库加载需要验证的证书列表，共 {} 个", certificatesToValidate.size());
            
            // 加载验证类型映射
            validationTypeMap = knowledgeBaseClient.getValidationTypeMapping();
            log.info("成功从知识库加载验证类型映射，共 {} 个", validationTypeMap.size());
            
        } catch (Exception e) {
            log.error("从知识库加载验证配置失败，使用默认配置", e);
            // 使用默认配置
            certificatesToValidate = new HashSet<>();
            validationTypeMap = new HashMap<>();
        }
    }

    /**
     * 判断证书是否需要验证
     *
     * @param certificate 证书对象
     * @return 如果需要验证返回true
     */
    private boolean shouldValidateCertificate(X509Certificate certificate) {
        String sha1 = certificate.getDerSha1();
        
        // 检查是否在需要验证的列表中
        if (certificatesToValidate.contains(sha1)) {
            return true;
        }
        
        // 检查证书标签，某些标签的证书需要验证
        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels != null) {
            // 自签名证书需要验证
            if (labels.contains(CertificateLabel.SELF_SIGNED)) {
                return true;
            }
            
            // 威胁证书需要验证
            if (labels.contains(CertificateLabel.THREAT)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 执行签名验证
     *
     * @param certificate 证书对象
     */
    private void performSignatureValidation(X509Certificate certificate) throws Exception {
        String sha1 = certificate.getDerSha1();
        
        // 确定验证级别
        CertificateValidator.ValidationLevel level = determineValidationLevel(certificate);
        
        // 创建验证器
        CertificateValidator validator = validatorFactory.createValidator(level);
        
        // 执行验证
        CertificateValidator.ValidationResult result = validator.validate(certificate);
        
        // 处理验证结果
        processValidationResult(certificate, result);
        
        log.debug("证书签名验证完成: {}, 结果: {}", sha1, result.isValid());
    }

    /**
     * 确定验证级别
     *
     * @param certificate 证书对象
     * @return 验证级别
     */
    private CertificateValidator.ValidationLevel determineValidationLevel(X509Certificate certificate) {
        String sha1 = certificate.getDerSha1();
        String validationType = validationTypeMap.get(sha1);
        
        if (validationType != null) {
            try {
                return CertificateValidator.ValidationLevel.valueOf(validationType);
            } catch (IllegalArgumentException e) {
                log.warn("无效的验证类型: {}, 使用默认级别", validationType);
            }
        }
        
        // 默认使用LEVEL_1验证
        return CertificateValidator.ValidationLevel.LEVEL_1;
    }

    /**
     * 处理验证结果
     *
     * @param certificate 证书对象
     * @param result 验证结果
     */
    private void processValidationResult(X509Certificate certificate, 
                                       CertificateValidator.ValidationResult result) {
        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null) {
            labels = new HashSet<>();
            certificate.setLabels(labels);
        }
        
        if (result.isValid()) {
            // 验证通过
            labels.add(CertificateLabel.SIGNATURE_VALID);
            log.debug("证书签名验证通过: {}", certificate.getDerSha1());
        } else {
            // 验证失败
            labels.add(CertificateLabel.SIGNATURE_INVALID);
            log.warn("证书签名验证失败: {}, 原因: {}", 
                    certificate.getDerSha1(), result.getErrorMessage());
        }
        
        // 添加验证级别标签
        labels.add(CertificateLabel.valueOf("VALIDATED_" + result.getValidationLevel().name()));
    }

    /**
     * 添加验证失败标签
     *
     * @param certificate 证书对象
     */
    private void addValidationFailedLabel(X509Certificate certificate) {
        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null) {
            labels = new HashSet<>();
            certificate.setLabels(labels);
        }
        labels.add(CertificateLabel.VALIDATION_ERROR);
    }
}
