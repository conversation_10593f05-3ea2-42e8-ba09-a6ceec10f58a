package com.geeksec.certificateanalyzer.operator.analysis.signature.validator;

import com.geeksec.certificateanalyzer.operator.analysis.signature.context.CertificateValidationContext;
import com.geeksec.certificateanalyzer.operator.analysis.signature.result.ValidationResult;

/**
 * 证书验证器接口
 * 定义证书验证的核心方法
 * 
 * <AUTHOR>
 */
public interface CertificateValidator {
    
    /**
     * 获取验证级别
     * 
     * @return 验证级别
     */
    ValidationLevel getValidationLevel();
    
    /**
     * 验证证书
     * 
     * @param context 验证上下文
     * @return 验证结果
     */
    ValidationResult validate(CertificateValidationContext context);
    
    /**
     * 验证级别枚举
     */
    enum ValidationLevel {
        LEVEL_1(1, "一级信任验证"),
        LEVEL_2(2, "二级信任验证"),
        LEVEL_3(3, "三级信任验证"),
        LEVEL_4(4, "四级信任验证"),
        LEVEL_5(5, "长链信任验证");
        
        private final int level;
        private final String description;
        
        ValidationLevel(int level, String description) {
            this.level = level;
            this.description = description;
        }
        
        public int getLevel() {
            return level;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
