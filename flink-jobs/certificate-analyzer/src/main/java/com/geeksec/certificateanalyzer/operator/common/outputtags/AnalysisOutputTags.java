package com.geeksec.certificateanalyzer.operator.common.outputtags;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * 证书分析阶段输出标签
 * 用于证书分析流程的流控制
 *
 * <AUTHOR>
 */
public final class AnalysisOutputTags {
    private AnalysisOutputTags() {
        // 防止实例化
    }

    /** 需要关联信息提取的证书标签 */
    public static final OutputTag<X509Certificate> NEED_RELATION_EXTRACTION =
        new OutputTag<>("need-relation-extraction", TypeInformation.of(X509Certificate.class));

    /** 需要基础分析的证书标签 */
    public static final OutputTag<X509Certificate> NEED_BASIC_ANALYSIS =
        new OutputTag<>("need-basic-analysis", TypeInformation.of(X509Certificate.class));

    /** 分析完成的证书标签 */
    public static final OutputTag<X509Certificate> ANALYSIS_COMPLETED =
        new OutputTag<>("analysis-completed", TypeInformation.of(X509Certificate.class));
}
