package com.geeksec.certificateanalyzer.operator.analysis.scoring;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 证书风险评分器
 *
 * 重构说明：
 * - 移除了对FileUtil和CSV文件的依赖
 * - 改为使用KnowledgeBaseClient从knowledge-base服务获取数据
 * - 保持原有的业务逻辑不变
 *
 * <AUTHOR>
 * @Date 2022/11/8
 * @Modified hufengkai - 重构为使用知识库服务
 * @Date 2024/12/22
 */

@Slf4j
public class CertificateRiskScorer extends RichMapFunction<X509Certificate, X509Certificate> {

    // 定义威胁分数名单，从知识库服务获取
    private HashMap<String, Integer> BLACK_SCORE_MAP = new HashMap<>();

    // 定义正常分数名单，从知识库服务获取
    private HashMap<String, Integer> WHITE_SCORE_MAP = new HashMap<>();

    // 标签告警名单 - TODO: 待knowledge-base服务补充API后替换
    private static HashMap<String, String> LABEL_ALARM_LIST = new HashMap<>();
    public static HashMap<String, HashMap<String, String>> LABEL_INFO_LIST = new HashMap<>();

    // 知识库客户端
    private KnowledgeBaseClient knowledgeBaseClient;

    @Override
    public void open(Configuration parameters) throws Exception {
        log.info("证书评分映射函数初始化开始");

        // 初始化知识库客户端
        String knowledgeBaseUrl = parameters.getString("knowledge.base.url", "http://knowledge-base:8080/knowledge-base");
        knowledgeBaseClient = new KnowledgeBaseClient(knowledgeBaseUrl);

        // 从知识库服务加载数据
        loadDataFromKnowledgeBase();

        log.info("证书评分映射函数初始化完成，黑名单评分: {} 条，白名单评分: {} 条",
                BLACK_SCORE_MAP.size(), WHITE_SCORE_MAP.size());
    }

    /**
     * 从知识库服务加载数据
     */
    private void loadDataFromKnowledgeBase() throws IOException {
        try {
            // 从知识库获取证书标签评分映射
            BLACK_SCORE_MAP = new HashMap<>(knowledgeBaseClient.getCertificateBlackScoreRemarkMap());
            WHITE_SCORE_MAP = new HashMap<>(knowledgeBaseClient.getCertificateWhiteScoreRemarkMap());

            log.info("成功从知识库加载证书标签评分数据，黑名单: {} 条，白名单: {} 条",
                    BLACK_SCORE_MAP.size(), WHITE_SCORE_MAP.size());

            // TODO: 待knowledge-base服务补充告警配置API后，替换以下临时实现
            // 临时保留空的映射，避免空指针异常
            LABEL_ALARM_LIST = new HashMap<>();
            LABEL_INFO_LIST = new HashMap<>();

            log.warn("告警配置数据暂时使用空映射，待knowledge-base服务补充相应API接口");

        } catch (Exception e) {
            log.error("从知识库加载证书标签评分数据失败，使用默认值", e);
            // 使用默认的空映射
            BLACK_SCORE_MAP = new HashMap<>();
            WHITE_SCORE_MAP = new HashMap<>();
            LABEL_ALARM_LIST = new HashMap<>();
            LABEL_INFO_LIST = new HashMap<>();
            throw new IOException("从知识库加载数据失败", e);
        }
    }

    @Override
    public X509Certificate map(X509Certificate cert) throws Exception {
        // 将CertificateLabel枚举转换为字符串列表用于评分计算
        ArrayList<String> labelStrings = cert.getLabels().stream()
                .map(CertificateLabel::name)
                .collect(Collectors.toCollection(ArrayList::new));

        // 计算威胁评分和信任评分
        cert.setThreatScore(getScore(labelStrings, BLACK_SCORE_MAP));
        cert.setTrustScore(getScore(labelStrings, WHITE_SCORE_MAP));
        cert.setImportTime(LocalDateTime.now());
        return cert;
    }

    /**
     * 标签名称转换方法（已废弃）
     *
     * 注意：此方法已不再使用，因为现在直接使用CertificateLabel枚举类型
     * 保留此方法是为了避免编译错误，但实际上已经不会被调用
     *
     * @deprecated 使用CertificateLabel枚举替代
     */
    @Deprecated
    public static List<String> tagsToTagId(List<Integer> labels) {
        // 返回空列表，因为此方法已不再使用
        return new ArrayList<>();
    }

    /**
     * 计算标签评分
     *
     * @param tags 标签列表
     * @param map 评分映射表
     * @return 计算得出的评分
     */
    private int getScore(ArrayList<String> tags, HashMap<String, Integer> map) {
        int score = 0;

        for (String tag : tags) {
            // 根据tag的值反查map中的标签对应的黑白名单对应的值，如果查不到就返回0
            Integer tagNumber = map.getOrDefault(tag, 0);
            score += tagNumber;
        }

        // 超过100取100
        return Math.min(score, 100);
    }

    @Override
    public void close() throws Exception {
        // 关闭知识库客户端
        if (knowledgeBaseClient != null) {
            knowledgeBaseClient.close();
        }
        super.close();
    }
}
