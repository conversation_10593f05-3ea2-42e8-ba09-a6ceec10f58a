package com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.apt;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.BaseCertificateDetector;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * PatchWork 相关证书检测器
 * 检测条件：
 * 1. 自签名证书 (SELF_SIGNED)
 * 2. 主题CN为 "testexp"
 */
@Slf4j
public class PatchWorkDetector extends BaseCertificateDetector {
    public static final String DETECTOR_NAME = "PatchWork Detector";
    private static final String PATCHWORK_CN = "testexp";
    
    public PatchWorkDetector() {
        super(DETECTOR_NAME);
    }
    
    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }
        
        Set<CertificateLabel> labels = cert.getLabels();
        if (isSuspiciousPatchWork(cert)) {
            labels.add(CertificateLabel.APT);
            labels.add(CertificateLabel.THREAT);
            log.debug("Detected PatchWork pattern in certificate: {}", cert.getCommonName());
        }
    }
    
    /**
     * 检查证书是否符合PatchWork的特征
     * 原始检测条件：
     * 1. 自签名证书 (SELF_SIGNED)
     * 2. 主题CN为 "testexp"
     */
    private boolean isSuspiciousPatchWork(X509Certificate cert) {
        return cert.getLabels().contains(CertificateLabel.SELF_SIGNED)
            && PATCHWORK_CN.equals(cert.getCommonName());
    }
}
