package com.geeksec.certificateanalyzer.operator.preprocessing;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.common.outputtags.PreprocessingOutputTags;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

/**
 * 证书预处理算子
 * <p>
 * 负责对输入的证书进行初步预处理，主要功能包括：
 * - 检查证书数据的完整性
 * - 将解析失败的证书分流到错误输出
 * - 将正常证书发送到主处理流程
 * <p>
 * 这是证书处理流水线的第一个算子，确保后续算子只处理有效的证书数据。
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificatePreprocessor extends ProcessFunction<X509Certificate, X509Certificate> {

    /**
     * 处理证书元素
     * <p>
     * 对每个输入的证书进行基础验证，将有效证书和无效证书分别路由到不同的输出流。
     *
     * @param certificate 输入的证书对象
     * @param ctx         处理上下文
     * @param out         主输出收集器
     * @throws Exception 处理过程中的异常
     */
    @Override
    public void processElement(X509Certificate certificate, Context ctx, Collector<X509Certificate> out) 
            throws Exception {
        
        // 检查证书数据的完整性
        if (isValidCertificate(certificate)) {
            // 将有效证书发送到正常处理流程
            ctx.output(PreprocessingOutputTags.NORMAL_CERTIFICATE, certificate);
            log.debug("证书预处理完成，证书ID: {}", certificate.getDerSha1());
        } else {
            // 将无效证书发送到错误处理流程
            ctx.output(PreprocessingOutputTags.CORRUPTED_CERTIFICATE, certificate);
            log.warn("发现损坏的证书数据，已路由到错误处理流程");
        }
    }

    /**
     * 验证证书数据的有效性
     * <p>
     * 检查证书对象和其包含的原始证书数据是否有效。
     *
     * @param certificate 待验证的证书对象
     * @return 如果证书有效返回true，否则返回false
     */
    private boolean isValidCertificate(X509Certificate certificate) {
        if (certificate == null) {
            log.debug("证书对象为null");
            return false;
        }

        if (certificate.getCert() == null) {
            log.debug("证书原始数据为null");
            return false;
        }

        if (certificate.getCert().length == 0) {
            log.debug("证书原始数据为空");
            return false;
        }

        return true;
    }
}
