package com.geeksec.certificateanalyzer.operator.analysis;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书威胁等级评估器
 * <p>
 * 提供证书分析流程中所需的业务逻辑计算，例如威胁等级评估。
 * 此类中的方法属于业务逻辑层，而非通用工具。
 *
 * <AUTHOR>
 */
@Slf4j
public final class ThreatLevelEvaluator {

    private ThreatLevelEvaluator() {
        throw new UnsupportedOperationException("评估器类不允许实例化");
    }

    /**
     * 评估证书威胁等级
     * 根据威胁评分计算证书的威胁等级
     *
     * @param threatScore 威胁评分
     * @return 威胁等级 (0-5)
     */
    public static int estimateThreatLevel(int threatScore) {
        if (threatScore <= 0) {
            return 0;
        } else if (threatScore <= 25) {
            return 1;
        } else if (threatScore <= 50) {
            return 2;
        } else if (threatScore <= 75) {
            return 3;
        } else if (threatScore < 100) {
            return 4;
        } else {
            return 5; // threatScore >= 100
        }
    }
}
