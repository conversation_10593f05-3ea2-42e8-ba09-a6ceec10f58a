package com.geeksec.certificateanalyzer.operator.common.outputtags;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * 证书去重阶段输出标签
 * 用于证书去重流程的流控制
 *
 * <AUTHOR>
 * @deprecated 大部分标签已不再使用，建议使用新的策略模式去重
 */
@Deprecated
public final class DeduplicationOutputTags {
    private DeduplicationOutputTags() {
        // 防止实例化
    }

    /** 正常证书去重后的标签 */
    public static final OutputTag<X509Certificate> CERTIFICATE_DEDUPLICATED =
        new OutputTag<>("certificate-deduplicated", TypeInformation.of(X509Certificate.class));

    /** 正常证书未去重的标签 */
    public static final OutputTag<X509Certificate> CERTIFICATE_NOT_DEDUPLICATED =
        new OutputTag<>("certificate-not-deduplicated", TypeInformation.of(X509Certificate.class));
}
