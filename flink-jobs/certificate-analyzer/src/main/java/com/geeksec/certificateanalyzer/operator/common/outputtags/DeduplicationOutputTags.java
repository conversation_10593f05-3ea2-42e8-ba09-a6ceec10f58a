package com.geeksec.certificateanalyzer.operator.common.outputtags;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * 证书去重阶段输出标签
 * 用于证书去重流程的流控制
 *
 * <AUTHOR>
 */
public final class DeduplicationOutputTags {
    private DeduplicationOutputTags() {
        // 防止实例化
    }

    /** 损坏证书去重后的标签 */
    public static final OutputTag<X509Certificate> CORRUPTED_CERTIFICATE_DEDUPLICATED =
        new OutputTag<>("corrupted-certificate-deduplicated", TypeInformation.of(X509Certificate.class));

    /** 损坏证书未去重的标签 */
    public static final OutputTag<X509Certificate> CORRUPTED_CERTIFICATE_NOT_DEDUPLICATED =
        new OutputTag<>("corrupted-certificate-not-deduplicated", TypeInformation.of(X509Certificate.class));

    /** 正常证书去重后的标签 */
    public static final OutputTag<X509Certificate> CERTIFICATE_DEDUPLICATED =
        new OutputTag<>("certificate-deduplicated", TypeInformation.of(X509Certificate.class));

    /** 正常证书未去重的标签 */
    public static final OutputTag<X509Certificate> CERTIFICATE_NOT_DEDUPLICATED =
        new OutputTag<>("certificate-not-deduplicated", TypeInformation.of(X509Certificate.class));
}
