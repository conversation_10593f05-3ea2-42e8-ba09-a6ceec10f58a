package com.geeksec.certificateanalyzer.operator.common.selectors;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import org.apache.flink.api.java.functions.KeySelector;

/**
 * <AUTHOR>
 * @Date 2023/2/15
 */

public class CertKeySelector implements KeySelector<X509Certificate, Integer> {
    private static final long serialVersionUID = 1429326005310979722L;
    private int parallelism;
    private Integer[] reBalanceKeys;

    public CertKeySelector(int parallelism) {
        this.parallelism = parallelism;
        reBalanceKeys = KeyReBalanceUtil.createReBalanceKeys(parallelism);
    }

    @Override
    public Integer getKey(X509Certificate cert) throws Exception {
        return reBalanceKeys[(int) (cert.getDuration() % parallelism)];
    }
}
