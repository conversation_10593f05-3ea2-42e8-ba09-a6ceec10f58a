package com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.apt;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.BaseCertificateDetector;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * APT29 相关证书检测器
 * 检测条件：
 * 1. 丢失的证书列表 (LOST_CERT_LIST)
 * 2. 特殊密钥ID (SPECIAL_KEY_ID)
 * 3. SAN中包含IP地址 (IP_IN_SAN)
 * 4. 颁发者中包含通配符 (WILDCARD_IN_ISSUER)
 * 5. 长期有效的证书 (LONG_DURATION_CERT)
 */
@Slf4j
public class APT29Detector extends BaseCertificateDetector {
    public static final String DETECTOR_NAME = "APT29 Detector";
    
    public APT29Detector() {
        super(DETECTOR_NAME);
    }
    
    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }
        
        Set<CertificateLabel> labels = cert.getLabels();
        if (isSuspiciousAPT29(cert)) {
            labels.add(CertificateLabel.APT);
            labels.add(CertificateLabel.THREAT);
            log.debug("Detected APT29 pattern in certificate: {}", cert.getCommonName());
        }
    }
    
    /**
     * 检查证书是否符合APT29的特征
     * @see #doDetect 查看检测条件
     */
    private boolean isSuspiciousAPT29(X509Certificate cert) {
        Set<CertificateLabel> labels = cert.getLabels();
        return labels.contains(CertificateLabel.LOST_CERT_LIST)
            && labels.contains(CertificateLabel.SPECIAL_KEY_ID)
            && labels.contains(CertificateLabel.IP_IN_SAN)
            && labels.contains(CertificateLabel.WILDCARD_IN_ISSUER)
            && labels.contains(CertificateLabel.LONG_VALIDITY_CERT);
    }
}
