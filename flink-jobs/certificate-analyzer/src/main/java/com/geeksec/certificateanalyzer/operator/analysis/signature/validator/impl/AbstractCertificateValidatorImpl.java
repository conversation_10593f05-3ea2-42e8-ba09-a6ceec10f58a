package com.geeksec.certificateanalyzer.operator.analysis.signature.validator.impl;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.signature.context.CertificateValidationContext;
import com.geeksec.certificateanalyzer.operator.analysis.signature.result.ValidationResult;
import com.geeksec.certificateanalyzer.operator.analysis.signature.validator.CertificateValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.*;

/**
 * 证书验证器抽象基类实现
 * 提供通用的Flink ProcessFunction实现和工具方法
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractCertificateValidatorImpl extends ProcessFunction<Row, Row>
        implements CertificateValidator {
    
    protected final ValidationLevel level;
    
    protected AbstractCertificateValidatorImpl(ValidationLevel level) {
        this.level = level;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        log.info("{} 初始化", getValidationLevel().getDescription());
    }
    
    @Override
    public void close() throws Exception {
        super.close();
        log.info("{} 关闭", getValidationLevel().getDescription());
    }
    
    @Override
    public void processElement(Row certificateRow, Context ctx, Collector<Row> out) throws Exception {
        try {
            if (certificateRow == null || certificateRow.getField(0) == null) {
                log.warn("{} 接收到空的证书数据", getValidationLevel().getDescription());
                return;
            }
            
            // 构建验证上下文
            CertificateValidationContext context = buildValidationContext(certificateRow, ctx, out);
            
            log.debug("{} 开始验证证书: {}", getValidationLevel().getDescription(), 
                    context.getCertificate().getDerSha1());
            
            // 执行验证
            ValidationResult result = validate(context);
            
            // 处理验证结果
            handleValidationResult(result, ctx);
            
        } catch (Exception e) {
            log.error("{} 处理证书时发生异常", getValidationLevel().getDescription(), e);
            out.collect(certificateRow);
        }
    }
    
    @Override
    public ValidationLevel getValidationLevel() {
        return level;
    }
    
    /**
     * 构建验证上下文
     */
    protected CertificateValidationContext buildValidationContext(Row certificateRow, Context ctx, Collector<Row> out) {
        X509Certificate certificate = (X509Certificate) certificateRow.getField(0);
        @SuppressWarnings("unchecked")
        Map<String, X509Certificate> parentCertificateMap = (Map<String, X509Certificate>) certificateRow.getField(1);
        
        Set<String> parentCertIds = certificate.getCertificateChain();
        List<String> parentCertIdList = parentCertIds != null ? 
            new ArrayList<>(parentCertIds) : new ArrayList<>();
        
        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null) {
            labels = new HashSet<>();
        }
        
        return CertificateValidationContext.builder()
                .certificate(certificate)
                .parentCertificateMap(parentCertificateMap != null ? parentCertificateMap : new HashMap<>())
                .parentCertificateIds(parentCertIdList)
                .securityLabels(labels)
                .flinkContext(ctx)
                .collector(out)
                .originalRow(certificateRow)
                .build();
    }
    
    /**
     * 处理验证结果
     */
    protected abstract void handleValidationResult(ValidationResult result, Context ctx);
    
    /**
     * 创建输出行数据
     */
    protected Row createOutputRow(X509Certificate certificate, Map<String, X509Certificate> parentCertificateMap) {
        Row outputRow = new Row(4);
        outputRow.setField(0, certificate);
        outputRow.setField(1, parentCertificateMap != null ? parentCertificateMap : new HashMap<>());
        outputRow.setField(2, certificate.getCertificateChain());
        outputRow.setField(3, certificate.getLabels());
        return outputRow;
    }
}
