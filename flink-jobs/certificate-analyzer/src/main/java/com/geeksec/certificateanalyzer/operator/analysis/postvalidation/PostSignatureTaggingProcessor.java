package com.geeksec.certificateanalyzer.pipeline.analysis.postvalidation;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.util.client.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 验签后标签处理器
 *
 * 在证书签名验证完成后进行更深入的威胁检测和标签分析
 *
 * <AUTHOR>
 */
@Slf4j
public class PostSignatureTaggingProcessor extends RichMapFunction<X509Certificate, X509Certificate> {

    // 威胁情报数据
    private Set<String> c2ThreatDomainList = new HashSet<>();
    private Set<String> c2ThreatIpList = new HashSet<>();
    private Set<String> iocDomainList = new HashSet<>();
    private Set<String> iocIpList = new HashSet<>();
    private Set<String> mineDomainList = new HashSet<>();
    private Set<String> aptDomainList = new HashSet<>();
    private Set<String> aptIpList = new HashSet<>();
    
    // 知识库客户端
    private KnowledgeBaseClient knowledgeBaseClient;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        log.info("初始化验签后标签处理功能");

        try {
            // 初始化知识库客户端
            knowledgeBaseClient = new KnowledgeBaseClient();

            // 加载威胁情报数据
            loadThreatIntelligenceData();

            log.info("验签后标签处理功能初始化完成");
        } catch (Exception e) {
            log.error("验签后标签处理功能初始化失败", e);
            throw new IOException("验签后标签处理功能初始化失败", e);
        }
    }

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("执行验签后标签处理，证书ID: {}", certificate.getDerSha1());

        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null) {
            labels = new HashSet<>();
        }

        // 第一阶段标签处理：基础威胁检测
        performStage1Labeling(certificate, labels);

        // 第二阶段标签处理：高级威胁检测
        performStage2Labeling(certificate, labels);

        certificate.setLabels(labels);
        return certificate;
    }

    /**
     * 第一阶段标签处理：基础威胁检测
     * 对应NTA 2.0中的CertTag1AfterSignMapFunction
     */
    private void performStage1Labeling(X509Certificate certificate, Set<CertificateLabel> labels) {
        log.debug("执行第一阶段标签处理，证书ID: {}", certificate.getDerSha1());

        // 域名相关检测
        analyzeDomainFeatures(certificate, labels);
        
        // 证书字段分析
        analyzeCertificateFields(certificate, labels);
        
        // 时间相关分析
        analyzeTemporalFeatures(certificate, labels);
        
        // 加密算法分析
        analyzeCryptographicFeatures(certificate, labels);
    }

    /**
     * 第二阶段标签处理：高级威胁检测
     * 对应NTA 2.0中的CertTag2AfterSignMapFunction
     */
    private void performStage2Labeling(X509Certificate certificate, Set<CertificateLabel> labels) {
        log.debug("执行第二阶段标签处理，证书ID: {}", certificate.getDerSha1());

        // 威胁情报检测
        detectThreatIntelligence(certificate, labels);
        
        // APT检测
        detectAPTThreats(certificate, labels);
        
        // C2通信检测
        detectC2Communication(certificate, labels);
        
        // 挖矿检测
        detectMiningThreats(certificate, labels);
        
        // Tor网络检测
        detectTorNetworks(certificate, labels);
    }

    /**
     * 域名特征分析
     */
    private void analyzeDomainFeatures(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> domains = certificate.getCertificateDomains();
        
        for (String domain : domains) {
            // 检测域名生成算法(DGA)
            if (isDGADomain(domain)) {
                labels.add(CertificateLabel.DGA_DOMAIN_CERT);
            }
            
            // 检测域名抢注
            if (isTyposquattingDomain(domain)) {
                labels.add(CertificateLabel.TYPOSQUATTING_CERT);
            }
            
            // 检测最近注册的域名
            if (isRecentlyRegisteredDomain(domain)) {
                labels.add(CertificateLabel.RECENTLY_REGISTERED);
            }
        }
    }

    /**
     * 证书字段分析
     */
    private void analyzeCertificateFields(X509Certificate certificate, Set<CertificateLabel> labels) {
        // 分析Subject字段
        String subject = certificate.getSubject();
        if (subject != null) {
            // 检测可疑的组织名称
            if (containsSuspiciousOrganization(subject)) {
                labels.add(CertificateLabel.SUSPICIOUS_ORGANIZATION);
            }
        }
        
        // 分析Issuer字段
        String issuer = certificate.getIssuer();
        if (issuer != null) {
            // 检测可疑的颁发者
            if (containsSuspiciousIssuer(issuer)) {
                labels.add(CertificateLabel.SUSPICIOUS_ISSUER);
            }
        }
    }

    /**
     * 时间相关分析
     */
    private void analyzeTemporalFeatures(X509Certificate certificate, Set<CertificateLabel> labels) {
        // 检测证书有效期异常
        long validityPeriod = certificate.getNotAfter() - certificate.getNotBefore();
        
        // 有效期过短（小于30天）
        if (validityPeriod < 30L * 24 * 60 * 60 * 1000) {
            labels.add(CertificateLabel.SHORT_VALIDITY_PERIOD);
        }
        
        // 有效期过长（大于10年）
        if (validityPeriod > 10L * 365 * 24 * 60 * 60 * 1000) {
            labels.add(CertificateLabel.LONG_VALIDITY_PERIOD);
        }
    }

    /**
     * 加密算法分析
     */
    private void analyzeCryptographicFeatures(X509Certificate certificate, Set<CertificateLabel> labels) {
        String signatureAlgorithm = certificate.getSignatureAlgorithm();
        
        if (signatureAlgorithm != null) {
            // 检测弱加密算法
            if (isWeakSignatureAlgorithm(signatureAlgorithm)) {
                labels.add(CertificateLabel.WEAK_SIGNATURE_ALGORITHM);
            }
        }
    }

    /**
     * 威胁情报检测
     */
    private void detectThreatIntelligence(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> domains = certificate.getCertificateDomains();
        List<String> ips = certificate.getCertificateIps();
        
        // 检测IOC域名
        for (String domain : domains) {
            if (iocDomainList.contains(domain)) {
                labels.add(CertificateLabel.IOC_DOMAIN_CERT);
                break;
            }
        }
        
        // 检测IOC IP
        for (String ip : ips) {
            if (iocIpList.contains(ip)) {
                labels.add(CertificateLabel.IOC_IP_CERT);
                break;
            }
        }
    }

    /**
     * APT检测
     */
    private void detectAPTThreats(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> domains = certificate.getCertificateDomains();
        List<String> ips = certificate.getCertificateIps();
        
        // 检测APT相关域名和IP
        for (String domain : domains) {
            if (aptDomainList.contains(domain)) {
                labels.add(CertificateLabel.APT_RELATED_CERT);
                break;
            }
        }
        
        for (String ip : ips) {
            if (aptIpList.contains(ip)) {
                labels.add(CertificateLabel.APT_RELATED_CERT);
                break;
            }
        }
    }

    /**
     * C2通信检测
     */
    private void detectC2Communication(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> domains = certificate.getCertificateDomains();
        List<String> ips = certificate.getCertificateIps();
        
        // 检测C2域名
        for (String domain : domains) {
            if (c2ThreatDomainList.contains(domain)) {
                labels.add(CertificateLabel.C2_THREAT_CERT);
                break;
            }
        }
        
        // 检测C2 IP
        for (String ip : ips) {
            if (c2ThreatIpList.contains(ip)) {
                labels.add(CertificateLabel.C2_THREAT_CERT);
                break;
            }
        }
    }

    /**
     * 挖矿检测
     */
    private void detectMiningThreats(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> domains = certificate.getCertificateDomains();
        
        for (String domain : domains) {
            if (mineDomainList.contains(domain)) {
                labels.add(CertificateLabel.MINING_CERT);
                break;
            }
        }
    }

    /**
     * Tor网络检测
     */
    private void detectTorNetworks(X509Certificate certificate, Set<CertificateLabel> labels) {
        List<String> domains = certificate.getCertificateDomains();
        
        for (String domain : domains) {
            if (domain.endsWith(".onion")) {
                if (domain.length() == 22) { // v2 onion address
                    labels.add(CertificateLabel.TOR_V2_CERT);
                } else if (domain.length() == 62) { // v3 onion address
                    labels.add(CertificateLabel.TOR_V3_CERT);
                }
            }
        }
    }

    /**
     * 加载威胁情报数据
     */
    private void loadThreatIntelligenceData() throws Exception {
        log.info("开始加载威胁情报数据");
        
        try {
            // 从知识库加载威胁情报数据
            c2ThreatDomainList = knowledgeBaseClient.getC2ThreatDomains();
            c2ThreatIpList = knowledgeBaseClient.getC2ThreatIps();
            iocDomainList = knowledgeBaseClient.getIocDomains();
            iocIpList = knowledgeBaseClient.getIocIps();
            mineDomainList = knowledgeBaseClient.getMiningDomains();
            aptDomainList = knowledgeBaseClient.getAptDomains();
            aptIpList = knowledgeBaseClient.getAptIps();
            
            log.info("威胁情报数据加载完成: C2域名={}, C2IP={}, IOC域名={}, IOC IP={}, 挖矿域名={}, APT域名={}, APT IP={}",
                    c2ThreatDomainList.size(), c2ThreatIpList.size(), iocDomainList.size(), 
                    iocIpList.size(), mineDomainList.size(), aptDomainList.size(), aptIpList.size());
                    
        } catch (Exception e) {
            log.error("加载威胁情报数据失败，使用默认空集合", e);
            // 使用默认的空集合
            c2ThreatDomainList = new HashSet<>();
            c2ThreatIpList = new HashSet<>();
            iocDomainList = new HashSet<>();
            iocIpList = new HashSet<>();
            mineDomainList = new HashSet<>();
            aptDomainList = new HashSet<>();
            aptIpList = new HashSet<>();
        }
    }

    // 辅助方法
    private boolean isDGADomain(String domain) {
        // 简化的DGA检测逻辑
        return domain.length() > 15 && !domain.contains("-") && 
               domain.chars().filter(ch -> Character.isDigit(ch)).count() > 3;
    }

    private boolean isTyposquattingDomain(String domain) {
        // 简化的域名抢注检测逻辑
        // 实际实现需要与知名域名进行相似度比较
        return false; // 占位实现
    }

    private boolean isRecentlyRegisteredDomain(String domain) {
        // 简化的最近注册检测逻辑
        // 实际实现需要查询域名注册时间
        return false; // 占位实现
    }

    private boolean containsSuspiciousOrganization(String subject) {
        // 简化的可疑组织检测逻辑
        String[] suspiciousKeywords = {"test", "temp", "fake", "malware"};
        String lowerSubject = subject.toLowerCase();
        for (String keyword : suspiciousKeywords) {
            if (lowerSubject.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    private boolean containsSuspiciousIssuer(String issuer) {
        // 简化的可疑颁发者检测逻辑
        return issuer.toLowerCase().contains("self") || issuer.toLowerCase().contains("unknown");
    }

    private boolean isWeakSignatureAlgorithm(String algorithm) {
        // 检测弱签名算法
        String lowerAlgorithm = algorithm.toLowerCase();
        return lowerAlgorithm.contains("md5") || lowerAlgorithm.contains("sha1");
    }
}
