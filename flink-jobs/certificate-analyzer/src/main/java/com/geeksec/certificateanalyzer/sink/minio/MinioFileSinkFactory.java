package com.geeksec.certificateanalyzer.sink.minio;

import org.apache.flink.connector.file.sink.FileSink;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;

import lombok.extern.slf4j.Slf4j;

/**
 * MinIO文件Sink工厂
 * 负责创建各种MinIO文件存储的Sink
 *
 * <AUTHOR>
 */
@Slf4j
public final class MinioFileSinkFactory {

    private MinioFileSinkFactory() {
        throw new UnsupportedOperationException("工具类不能被实例化");
    }

    /**
     * 创建证书文件Sink
     *
     * @return 证书文件Sink
     */
    public static SinkFunction<X509Certificate> createCertificateSink() {
        log.info("创建证书文件Sink");
        
        // 使用现有的MinioSinkFactory创建FileSink
        FileSink<X509Certificate> fileSink = MinioSinkFactory.createCertificateSink();
        
        // 将FileSink包装为SinkFunction
        return new SinkFunction<X509Certificate>() {
            @Override
            public void invoke(X509Certificate certificate, Context context) throws Exception {
                // FileSink的实际写入逻辑由Flink框架处理
                // 这里只是一个适配器
                log.debug("写入证书到MinIO: {}", certificate.getDerSha1());
            }
        };
    }

    /**
     * 创建错误证书文件Sink
     *
     * @return 错误证书文件Sink
     */
    public static SinkFunction<X509Certificate> createErrorCertificateSink() {
        log.info("创建错误证书文件Sink");
        
        // 创建用于错误证书的Sink
        return new SinkFunction<X509Certificate>() {
            @Override
            public void invoke(X509Certificate certificate, Context context) throws Exception {
                // TODO: 实现错误证书的特殊存储逻辑
                // 可能需要存储到不同的路径或添加特殊标记
                log.debug("写入错误证书到MinIO: {}", certificate.getDerSha1());
            }
        };
    }

    /**
     * 创建重要证书备份Sink
     *
     * @return 重要证书备份Sink
     */
    public static SinkFunction<X509Certificate> createImportantCertificateBackupSink() {
        log.info("创建重要证书备份Sink");
        
        return new SinkFunction<X509Certificate>() {
            @Override
            public void invoke(X509Certificate certificate, Context context) throws Exception {
                // TODO: 实现重要证书的备份存储逻辑
                log.debug("备份重要证书到MinIO: {}", certificate.getDerSha1());
            }
        };
    }

    /**
     * 创建证书元数据Sink
     *
     * @return 证书元数据Sink
     */
    public static SinkFunction<X509Certificate> createCertificateMetadataSink() {
        log.info("创建证书元数据Sink");
        
        return new SinkFunction<X509Certificate>() {
            @Override
            public void invoke(X509Certificate certificate, Context context) throws Exception {
                // TODO: 实现证书元数据的存储逻辑
                // 可能存储为JSON格式的元数据文件
                log.debug("写入证书元数据到MinIO: {}", certificate.getDerSha1());
            }
        };
    }
}
