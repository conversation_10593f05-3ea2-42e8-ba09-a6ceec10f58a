package com.geeksec.certificateanalyzer.operator.common.outputtags;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * 证书威胁告警输出标签
 * 用于证书威胁检测和告警流程的流控制
 *
 * <AUTHOR>
 */
public final class AlarmOutputTags {
    private AlarmOutputTags() {
        // 防止实例化
    }

    /** 已知APT证书告警标签 */
    public static final OutputTag<X509Certificate> KNOWN_APT_CERTIFICATE_ALARM =
        new OutputTag<>("known-apt-certificate-alarm", TypeInformation.of(X509Certificate.class));

    /** 已知威胁证书告警标签 */
    public static final OutputTag<X509Certificate> KNOWN_THREAT_CERTIFICATE_ALARM =
        new OutputTag<>("known-threat-certificate-alarm", TypeInformation.of(X509Certificate.class));

    /** 僵尸网络证书检测告警标签 */
    public static final OutputTag<X509Certificate> BOTNET_CERTIFICATE_ALARM =
        new OutputTag<>("botnet-certificate-alarm", TypeInformation.of(X509Certificate.class));

    /** 恶意证书检测告警标签 */
    public static final OutputTag<X509Certificate> MALICIOUS_CERTIFICATE_ALARM =
        new OutputTag<>("malicious-certificate-alarm", TypeInformation.of(X509Certificate.class));
}
