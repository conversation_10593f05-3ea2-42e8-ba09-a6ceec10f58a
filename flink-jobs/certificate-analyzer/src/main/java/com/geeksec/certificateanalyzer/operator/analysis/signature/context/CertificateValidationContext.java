package com.geeksec.certificateanalyzer.operator.analysis.signature.context;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import lombok.Builder;
import lombok.Getter;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 证书验证上下文
 * 封装证书验证所需的所有参数和上下文信息
 * 
 * <AUTHOR>
 */
@Getter
@Builder
public class CertificateValidationContext {
    
    private final X509Certificate certificate;
    private final Map<String, X509Certificate> parentCertificateMap;
    private final List<String> parentCertificateIds;
    private final Set<CertificateLabel> securityLabels;
    private final ProcessFunction<Row, Row>.Context flinkContext;
    private final Collector<Row> collector;
    private final Row originalRow;
    

}
