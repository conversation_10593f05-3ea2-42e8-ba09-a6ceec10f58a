package com.geeksec.certificateanalyzer.operator.preprocessing.deduplication;

import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;

import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.repository.redis.CertificateRedisBloomFilter;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

/**
 * 轻量级布隆过滤器去重处理器
 * 只使用布隆过滤器进行初步过滤，简化去重逻辑
 * 主要用于流处理性能优化
 *
 * <AUTHOR>
 * @date 2024/12/17
 */
@Slf4j
public class BloomFilterDeduplicator extends RichFlatMapFunction<X509Certificate, X509Certificate> {

    private static final long serialVersionUID = 1L;

    /** 证书来源类型 - 导入的证书（用户证书） */
    private static final Integer CERT_SOURCE_IMPORTED = 1;

    private transient JedisPool jedisPool;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // 初始化Redis连接池
        jedisPool = CertificateRedisBloomFilter.initJedisPool();
        log.info("轻量级布隆过滤器去重处理器初始化完成");
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (jedisPool != null) {
            jedisPool.close();
        }
        log.info("轻量级布隆过滤器去重处理器关闭完成");
    }

    @Override
    public void flatMap(X509Certificate cert, Collector<X509Certificate> collector) throws Exception {
        // 对于用户证书，直接通过（用户证书需要保留所有实例用于业务逻辑处理）
        if (CERT_SOURCE_IMPORTED.equals(cert.getSource())) {
            collector.collect(cert);
            return;
        }

        // 对于系统证书，使用布隆过滤器进行轻量级去重
        Jedis jedis = null;
        try {
            jedis = CertificateRedisBloomFilter.getJedis(jedisPool);
            
            // 只使用布隆过滤器检查
            boolean mightExist = CertificateRedisBloomFilter.checkBloomFilter(cert, jedis);
            
            if (!mightExist) {
                // 证书肯定不存在，添加到布隆过滤器并继续处理
                CertificateRedisBloomFilter.addToBloomFilter(cert, jedis);
                cert.setCertOccurrenceCount(1);
                collector.collect(cert);
                log.debug("新证书通过布隆过滤器: {}", cert.getDerSha1());
            } else {
                // 证书可能存在，为了性能考虑直接过滤掉
                // 注意：这里可能有误判，但为了性能优化可以接受
                log.debug("证书可能重复，被布隆过滤器过滤: {}", cert.getDerSha1());
            }
        } catch (Exception e) {
            log.error("布隆过滤器去重失败，为安全起见继续处理证书: SHA1={}, error={}",
                    cert.getDerSha1(), e.getMessage());
            // 出错时，为了安全起见，继续处理证书
            cert.setCertOccurrenceCount(1);
            collector.collect(cert);
        } finally {
            if (jedis != null) {
                jedis.close();
            }
        }
    }
}
