package com.geeksec.certificateanalyzer.operator.common.outputtags;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * 证书签名验证阶段输出标签
 * 用于证书签名验证流程的流控制
 *
 * <AUTHOR>
 */
public final class ValidationOutputTags {
    private ValidationOutputTags() {
        // 防止实例化
    }

    // ==================== 流程控制标签 ====================

    /** 转到验证流程的侧输出标签 */
    public static final OutputTag<Row> GOTO_VALIDATION =
        new OutputTag<>("goto-validation", TypeInformation.of(Row.class));

    /** 转到评估流程的侧输出标签 */
    public static final OutputTag<Row> GOTO_EVALUATION =
        new OutputTag<>("goto-evaluation", TypeInformation.of(Row.class));

    // ==================== 分级验证器输出标签 ====================

    /** 第一级签名验证完成的侧输出标签 */
    public static final OutputTag<Row> STOP_VALIDATION_1 =
        new OutputTag<>("stop_validation_1", TypeInformation.of(Row.class));

    /** 第一级签名验证继续的侧输出标签 */
    public static final OutputTag<Row> CONTINUE_VALIDATION_1 =
        new OutputTag<>("continue_validation_1", TypeInformation.of(Row.class));

    /** 第二级签名验证完成的侧输出标签 */
    public static final OutputTag<Row> STOP_VALIDATION_2 =
        new OutputTag<>("stop_validation_2", TypeInformation.of(Row.class));

    /** 第二级签名验证继续的侧输出标签 */
    public static final OutputTag<Row> CONTINUE_VALIDATION_2 =
        new OutputTag<>("continue_validation_2", TypeInformation.of(Row.class));

    /** 第三级签名验证完成的侧输出标签 */
    public static final OutputTag<Row> STOP_VALIDATION_3 =
        new OutputTag<>("stop_validation_3", TypeInformation.of(Row.class));

    /** 第三级签名验证继续的侧输出标签 */
    public static final OutputTag<Row> CONTINUE_VALIDATION_3 =
        new OutputTag<>("continue_validation_3", TypeInformation.of(Row.class));

    /** 第四级签名验证完成的侧输出标签 */
    public static final OutputTag<Row> STOP_VALIDATION_4 =
        new OutputTag<>("stop_validation_4", TypeInformation.of(Row.class));

    /** 第四级签名验证继续的侧输出标签 */
    public static final OutputTag<Row> CONTINUE_VALIDATION_4 =
        new OutputTag<>("continue_validation_4", TypeInformation.of(Row.class));

    /** 第五级签名验证完成的侧输出标签 */
    public static final OutputTag<Row> STOP_VALIDATION_5 =
        new OutputTag<>("stop_validation_5", TypeInformation.of(Row.class));
}
