package com.geeksec.task.application.service;

import com.geeksec.task.application.service.impl.PcapImportManagerServiceImpl;
import com.geeksec.task.model.dto.PcapImportRequestDto;
import com.geeksec.task.model.dto.PcapImportResultDto;
import com.geeksec.task.model.vo.ImportServiceStatusVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PCAP 导入管理服务测试类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
class PcapImportManagerServiceTest {

    @Mock
    private ServiceStatusManager serviceStatusManager;

    @Mock
    private PcapFileProcessorService pcapFileProcessorService;

    @Mock
    private RuleSyncService ruleSyncService;

    @Mock
    private TaskBatchManagerService taskBatchManagerService;

    @InjectMocks
    private PcapImportManagerServiceImpl pcapImportManagerService;

    private PcapImportRequestDto testRequest;

    @BeforeEach
    void setUp() {
        testRequest = new PcapImportRequestDto();
        testRequest.setTaskId(1001);
        testRequest.setBatchId(2001);
        testRequest.setInputPaths(Arrays.asList("/test/path1.pcap", "/test/path2.pcap"));
        testRequest.setBatchDescription("测试批次");
        testRequest.setFullflowState("OFF");
        testRequest.setFlowlogState("ON");
    }

    @Test
    void testFindIdleService_Success() {
        // 准备测试数据
        ImportServiceStatusVo idleService = ImportServiceStatusVo.createIdleService(1, "offline_pcap_import.1.service");
        ImportServiceStatusVo activeService = ImportServiceStatusVo.createActiveService(2, "offline_pcap_import.2.service", 1001, 2001);
        
        when(serviceStatusManager.getAllServiceStatus()).thenReturn(Arrays.asList(activeService, idleService));

        // 执行测试
        Integer serviceId = pcapImportManagerService.findIdleService();

        // 验证结果
        assertNotNull(serviceId);
        assertEquals(1, serviceId);
    }

    @Test
    void testFindIdleService_NoIdleService() {
        // 准备测试数据 - 所有服务都在忙碌
        ImportServiceStatusVo activeService1 = ImportServiceStatusVo.createActiveService(1, "offline_pcap_import.1.service", 1001, 2001);
        ImportServiceStatusVo activeService2 = ImportServiceStatusVo.createActiveService(2, "offline_pcap_import.2.service", 1002, 2002);
        
        when(serviceStatusManager.getAllServiceStatus()).thenReturn(Arrays.asList(activeService1, activeService2));
        when(serviceStatusManager.getServiceStatus(anyInt())).thenReturn(null);
        when(serviceStatusManager.createOrRestartService(anyInt())).thenReturn(false);

        // 执行测试
        Integer serviceId = pcapImportManagerService.findIdleService();

        // 验证结果
        assertNull(serviceId);
    }

    @Test
    void testGetAllServiceStatus() {
        // 准备测试数据
        List<ImportServiceStatusVo> expectedStatus = Arrays.asList(
            ImportServiceStatusVo.createIdleService(1, "offline_pcap_import.1.service"),
            ImportServiceStatusVo.createActiveService(2, "offline_pcap_import.2.service", 1001, 2001)
        );
        
        when(serviceStatusManager.getAllServiceStatus()).thenReturn(expectedStatus);

        // 执行测试
        List<ImportServiceStatusVo> actualStatus = pcapImportManagerService.getAllServiceStatus();

        // 验证结果
        assertNotNull(actualStatus);
        assertEquals(2, actualStatus.size());
        assertEquals(expectedStatus, actualStatus);
    }

    @Test
    void testGetServiceStatus() {
        // 准备测试数据
        Integer serviceId = 1;
        ImportServiceStatusVo expectedStatus = ImportServiceStatusVo.createIdleService(serviceId, "offline_pcap_import.1.service");
        
        when(serviceStatusManager.getServiceStatus(serviceId)).thenReturn(expectedStatus);

        // 执行测试
        ImportServiceStatusVo actualStatus = pcapImportManagerService.getServiceStatus(serviceId);

        // 验证结果
        assertNotNull(actualStatus);
        assertEquals(expectedStatus, actualStatus);
        assertEquals(serviceId, actualStatus.getServiceId());
    }

    @Test
    void testStartImportService() {
        // 准备测试数据
        Integer serviceId = 1;
        Integer taskId = 1001;
        Integer batchId = 2001;
        
        when(serviceStatusManager.startService(serviceId, taskId, batchId)).thenReturn(true);

        // 执行测试
        boolean result = pcapImportManagerService.startImportService(serviceId, taskId, batchId);

        // 验证结果
        assertTrue(result);
        verify(serviceStatusManager).startService(serviceId, taskId, batchId);
    }

    @Test
    void testStopImportService() {
        // 准备测试数据
        Integer serviceId = 1;
        
        when(serviceStatusManager.stopService(serviceId)).thenReturn(true);

        // 执行测试
        boolean result = pcapImportManagerService.stopImportService(serviceId);

        // 验证结果
        assertTrue(result);
        verify(serviceStatusManager).stopService(serviceId);
    }

    @Test
    void testGetRunningTaskCount() {
        // 准备测试数据
        List<ImportServiceStatusVo> serviceStatus = Arrays.asList(
            ImportServiceStatusVo.createActiveService(1, "service1", 1001, 2001),
            ImportServiceStatusVo.createIdleService(2, "service2"),
            ImportServiceStatusVo.createActiveService(3, "service3", 1002, 2002)
        );
        
        when(serviceStatusManager.getAllServiceStatus()).thenReturn(serviceStatus);

        // 执行测试
        int runningCount = pcapImportManagerService.getRunningTaskCount();

        // 验证结果
        assertEquals(2, runningCount);
    }

    @Test
    void testGetMaxConcurrentTasks() {
        // 执行测试
        int maxTasks = pcapImportManagerService.getMaxConcurrentTasks();

        // 验证结果 - 默认值应该是3
        assertEquals(3, maxTasks);
    }
}
