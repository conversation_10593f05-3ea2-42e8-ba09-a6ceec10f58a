package com.geeksec.task.integration;

import com.geeksec.task.application.service.ImportTaskScheduler;
import com.geeksec.task.application.service.PcapImportManagerService;
import com.geeksec.task.application.service.ServiceStatusManager;
import com.geeksec.task.model.dto.PcapImportRequestDto;
import com.geeksec.task.model.dto.PcapImportResultDto;
import com.geeksec.task.model.vo.ImportServiceStatusVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PCAP 导入集成测试
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
class PcapImportIntegrationTest {

    @Autowired
    private PcapImportManagerService pcapImportManagerService;

    @Autowired
    private ImportTaskScheduler importTaskScheduler;

    @Autowired
    private ServiceStatusManager serviceStatusManager;

    @Test
    void testServiceStatusManager() {
        // 测试服务状态管理器
        List<ImportServiceStatusVo> allStatus = serviceStatusManager.getAllServiceStatus();
        assertNotNull(allStatus);
        
        // 应该有默认的服务实例
        assertFalse(allStatus.isEmpty());
        
        // 检查第一个服务实例
        ImportServiceStatusVo firstService = allStatus.get(0);
        assertNotNull(firstService.getServiceId());
        assertNotNull(firstService.getServiceName());
        assertEquals(ImportServiceStatusVo.ServiceStatus.IDLE, firstService.getStatus());
    }

    @Test
    void testImportTaskScheduler() {
        // 测试任务调度器
        assertTrue(importTaskScheduler.isRunning());
        
        // 获取调度器状态
        ImportTaskScheduler.SchedulerStatus status = importTaskScheduler.getSchedulerStatus();
        assertNotNull(status);
        assertTrue(status.isRunning());
        assertEquals(0, status.getPendingTaskCount());
    }

    @Test
    void testPcapImportManagerService() {
        // 测试 PCAP 导入管理服务
        
        // 1. 测试获取所有服务状态
        List<ImportServiceStatusVo> allStatus = pcapImportManagerService.getAllServiceStatus();
        assertNotNull(allStatus);
        assertFalse(allStatus.isEmpty());
        
        // 2. 测试查找空闲服务
        Integer idleServiceId = pcapImportManagerService.findIdleService();
        assertNotNull(idleServiceId);
        assertTrue(idleServiceId > 0);
        
        // 3. 测试获取指定服务状态
        ImportServiceStatusVo serviceStatus = pcapImportManagerService.getServiceStatus(idleServiceId);
        assertNotNull(serviceStatus);
        assertEquals(idleServiceId, serviceStatus.getServiceId());
        
        // 4. 测试获取运行任务数量
        int runningCount = pcapImportManagerService.getRunningTaskCount();
        assertTrue(runningCount >= 0);
        
        // 5. 测试获取最大并发任务数
        int maxConcurrent = pcapImportManagerService.getMaxConcurrentTasks();
        assertTrue(maxConcurrent > 0);
    }

    @Test
    void testSubmitImportTask() {
        // 创建测试请求
        PcapImportRequestDto request = new PcapImportRequestDto();
        request.setTaskId(9999);
        request.setBatchId(8888);
        request.setInputPaths(Arrays.asList("/test/sample1.pcap", "/test/sample2.pcap"));
        request.setBatchDescription("集成测试批次");
        request.setFullflowState("OFF");
        request.setFlowlogState("ON");
        request.setPriority(5);
        
        // 提交任务到调度器
        PcapImportResultDto result = importTaskScheduler.submitImportTask(request);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals("任务已提交到调度队列", result.getMessage());
        assertEquals(request.getTaskId(), result.getTaskId());
        assertEquals(request.getBatchId(), result.getBatchId());
        
        // 验证队列中有任务
        assertTrue(importTaskScheduler.getPendingTaskCount() > 0);
        
        // 获取待处理任务
        List<PcapImportRequestDto> pendingTasks = importTaskScheduler.getPendingTasks();
        assertNotNull(pendingTasks);
        assertFalse(pendingTasks.isEmpty());
        
        // 验证任务内容
        PcapImportRequestDto pendingTask = pendingTasks.get(0);
        assertEquals(request.getTaskId(), pendingTask.getTaskId());
        assertEquals(request.getBatchId(), pendingTask.getBatchId());
        
        // 清空队列
        int clearedCount = importTaskScheduler.clearTaskQueue();
        assertTrue(clearedCount > 0);
        assertEquals(0, importTaskScheduler.getPendingTaskCount());
    }

    @Test
    void testServiceOperations() {
        // 获取一个空闲服务
        Integer serviceId = pcapImportManagerService.findIdleService();
        assertNotNull(serviceId);
        
        // 测试启动服务
        boolean started = pcapImportManagerService.startImportService(serviceId, 1001, 2001);
        assertTrue(started);
        
        // 验证服务状态变为活跃
        ImportServiceStatusVo status = pcapImportManagerService.getServiceStatus(serviceId);
        assertNotNull(status);
        // 注意：由于是异步操作，状态可能还没有立即更新
        
        // 测试停止服务
        boolean stopped = pcapImportManagerService.stopImportService(serviceId);
        assertTrue(stopped);
        
        // 验证服务状态变为停止
        status = pcapImportManagerService.getServiceStatus(serviceId);
        assertNotNull(status);
        assertEquals(ImportServiceStatusVo.ServiceStatus.STOPPED, status.getStatus());
    }

    @Test
    void testSchedulerOperations() {
        // 测试停止调度器
        importTaskScheduler.stopScheduler();
        assertFalse(importTaskScheduler.isRunning());
        
        // 测试启动调度器
        importTaskScheduler.startScheduler();
        assertTrue(importTaskScheduler.isRunning());
        
        // 验证调度器状态
        ImportTaskScheduler.SchedulerStatus status = importTaskScheduler.getSchedulerStatus();
        assertTrue(status.isRunning());
    }
}
