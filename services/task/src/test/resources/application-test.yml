# 测试环境配置
server:
  port: 0  # 随机端口

spring:
  application:
    name: task-service-test
  profiles:
    active: test

  # 测试数据源配置 - 使用内存数据库
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    
    # Druid 连接池配置（简化）
    druid:
      initial-size: 1
      min-idle: 1
      max-active: 5
      max-wait: 60000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false

  # 测试 Redis 配置 - 使用嵌入式 Redis
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 15  # 使用测试专用数据库
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 2
          max-wait: -1ms
          max-idle: 2
          min-idle: 0

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non-null

# MyBatis-Flex配置
mybatis-flex:
  type-aliases-package: com.geeksec.task.model.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# 日志配置
logging:
  level:
    '[com.geeksec.task]': DEBUG
    '[org.springframework.web]': DEBUG
    '[org.springframework.test]': DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# 测试专用 MinIO 配置
minio:
  endpoint: http://localhost:9000
  access-key: testuser
  secret-key: testpass
  pcap-bucket: test-pcap-files
  default-bucket: test-data
  path-style-access: true
  region: us-east-1

# 测试专用探针服务配置
probe:
  base-url: http://localhost:59000
  stop-offline-thd: /offline/stopOfflineThd
  sync-config: /thd/task_check
  sync-rules: /thd/rule_syn
  connect-timeout: 1000
  read-timeout: 5000

# 测试任务配置
task:
  enabled:
    task_scheduled: false  # 测试时禁用定时任务

  file:
    tmp-path: /tmp/test/template/
    pcap-download-path: /tmp/test/download/pcap/
    session-path: /tmp/test/download/logs/
    template-path: /tmp/test/rule/template/
    pcap-path: /tmp/test/import/pcap/
    server-pcap-path: /tmp/test/pcapfiles/

# NTA 3.0 测试配置
nta:
  task:
    pcap-import:
      # 测试环境使用较小的并发数
      max-concurrent-tasks: 2
      scheduler:
        enabled: true
        max-queue-size: 10
        schedule-interval: 1000  # 更快的调度间隔用于测试
      rule-config:
        base-path: /tmp/test/nta/rule-config

# 监控配置（测试时简化）
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

# Swagger配置（测试时禁用）
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
