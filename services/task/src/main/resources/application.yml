server:
  port: 8087
  servlet:
    context-path: /api/task

spring:
  application:
    name: task-service
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
    include: common

  # 数据源配置 - 统一使用PostgreSQL + Druid
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:nta}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:nta}
    password: ${DB_PASSWORD:nta123}

    # Druid 连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

      # 监控配置
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: ${DRUID_USERNAME:admin}
        login-password: ${DRUID_PASSWORD:admin123}

  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non-null

# MyBatis-Flex配置
mybatis-flex:
  type-aliases-package: com.geeksec.task.model.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# 分页插件配置
pagehelper:
  helper-dialect: postgresql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 日志配置
logging:
  level:
    '[com.geeksec.task]': DEBUG
    '[com.geeksec.task.infrastructure.mapper]': DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# MinIO 对象存储配置
minio:
  # MinIO 服务端点
  endpoint: ${MINIO_ENDPOINT:http://localhost:9000}
  # 访问密钥
  access-key: ${MINIO_ACCESS_KEY:minioadmin}
  # 秘密密钥
  secret-key: ${MINIO_SECRET_KEY:minioadmin}
  # PCAP文件存储桶名称
  pcap-bucket: ${MINIO_PCAP_BUCKET:nta-pcap-files}
  # 默认存储桶名称
  default-bucket: ${MINIO_DEFAULT_BUCKET:nta-data}
  # 是否启用路径样式访问
  path-style-access: ${MINIO_PATH_STYLE_ACCESS:true}
  # 区域
  region: ${MINIO_REGION:us-east-1}

# 探针服务配置
probe:
  # 探针服务基础地址
  base-url: ${PROBE_BASE_URL:http://localhost:59000}
  # 停止离线线程接口
  stop-offline-thd: ${PROBE_STOP_OFFLINE_THD:/offline/stopOfflineThd}
  # 配置同步接口
  sync-config: ${PROBE_SYNC_CONFIG:/thd/task_check}
  # 规则同步接口
  sync-rules: ${PROBE_SYNC_RULES:/thd/rule_syn}
  # 连接超时时间（毫秒）
  connect-timeout: ${PROBE_CONNECT_TIMEOUT:5000}
  # 读取超时时间（毫秒）
  read-timeout: ${PROBE_READ_TIMEOUT:30000}

# 分析任务配置
task:
  # 是否启用定时任务调度
  enabled:
    task_scheduled: true

  # 文件路径配置
  file:
    # 临时文件路径
    tmp-path: /opt/local/template/
    # PCAP下载路径（离线分析任务使用）
    pcap-download-path: /data/download/pcap/
    # 会话下载路径
    session-path: /data/download/logs/
    # 模板文件路径
    template-path: /opt/rule/template/
    # PCAP文件存放目录（已迁移到MinIO）
    pcap-path: /opt/GeekSecData/import/pcap/
    # 服务器PCAP文件存放目录（已迁移到MinIO）
    server-pcap-path: /data/pcapfiles/

# NTA 3.0 特定配置
nta:
  task:
    # PCAP导入管理配置
    pcap-import:
      # 最大并发导入任务数量
      max-concurrent-tasks: ${PCAP_IMPORT_MAX_CONCURRENT:3}
      # 任务调度器配置
      scheduler:
        # 是否启用任务调度器
        enabled: ${PCAP_IMPORT_SCHEDULER_ENABLED:true}
        # 任务队列最大容量
        max-queue-size: ${PCAP_IMPORT_MAX_QUEUE_SIZE:100}
        # 调度间隔（毫秒）
        schedule-interval: ${PCAP_IMPORT_SCHEDULE_INTERVAL:5000}
      # 规则配置路径
      rule-config:
        # 规则配置基础路径
        base-path: ${PCAP_IMPORT_RULE_CONFIG_PATH:/tmp/nta/rule-config}

  # 外部服务URL配置 - 已废弃，使用MinIO替代
  external:
    # 数据导入相关接口 - 已迁移到MinIO服务
    # list-files-url: ${IMPORT_SERVICE_URL:http://localhost:5000}/offline/listServerPath
    # check-files-url: ${IMPORT_SERVICE_URL:http://localhost:5000}/offline/checkFilePaths
    # delete-batch-pcaps: ${IMPORT_SERVICE_URL:http://localhost:5000}/offline/deletePcapFiles
    stop-batch-offline-thd: ${IMPORT_SERVICE_URL:http://localhost:5000}/offline/stopOfflineThd

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# Swagger配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: NTA 分析任务管理服务 API
    description: NTA 3.0 流量分析任务管理微服务接口文档，包括实时分析任务（从NIC采集流量数据）和离线分析任务（读取pcap文件）
    version: 3.0.0
    contact:
      name: NTA Team
      email: <EMAIL>
