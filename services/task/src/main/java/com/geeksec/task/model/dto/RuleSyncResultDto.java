package com.geeksec.task.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 规则同步结果 DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class RuleSyncResultDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 同步是否成功
     */
    private Boolean success;

    /**
     * 结果消息
     */
    private String message;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 批次ID
     */
    private Integer batchId;

    /**
     * 服务实例ID
     */
    private Integer serviceId;

    /**
     * 同步的规则数量
     */
    private Integer ruleCount;

    /**
     * 同步的过滤规则数量
     */
    private Integer filterRuleCount;

    /**
     * 生成的配置文件路径列表
     */
    private List<String> configFilePaths;

    /**
     * 全流量留存状态
     */
    private String fullflowState;

    /**
     * 流日志状态
     */
    private String flowlogState;

    /**
     * 同步开始时间
     */
    private LocalDateTime syncStartTime;

    /**
     * 同步结束时间
     */
    private LocalDateTime syncEndTime;

    /**
     * 错误详情（如果有）
     */
    private String errorDetail;

    /**
     * 创建成功结果
     */
    public static RuleSyncResultDto success(Integer taskId, Integer batchId, Integer serviceId) {
        RuleSyncResultDto result = new RuleSyncResultDto();
        result.setSuccess(true);
        result.setMessage("规则同步成功");
        result.setTaskId(taskId);
        result.setBatchId(batchId);
        result.setServiceId(serviceId);
        result.setSyncStartTime(LocalDateTime.now());
        result.setSyncEndTime(LocalDateTime.now());
        return result;
    }

    /**
     * 创建失败结果
     */
    public static RuleSyncResultDto failure(Integer taskId, Integer batchId, Integer serviceId, 
                                          String message, String errorDetail) {
        RuleSyncResultDto result = new RuleSyncResultDto();
        result.setSuccess(false);
        result.setMessage(message);
        result.setTaskId(taskId);
        result.setBatchId(batchId);
        result.setServiceId(serviceId);
        result.setErrorDetail(errorDetail);
        result.setSyncStartTime(LocalDateTime.now());
        result.setSyncEndTime(LocalDateTime.now());
        return result;
    }
}
