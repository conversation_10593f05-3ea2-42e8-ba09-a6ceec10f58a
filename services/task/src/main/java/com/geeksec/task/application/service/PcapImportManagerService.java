package com.geeksec.task.application.service;

import com.geeksec.task.model.dto.PcapImportRequestDto;
import com.geeksec.task.model.dto.PcapImportResultDto;
import com.geeksec.task.model.vo.ImportServiceStatusVo;

import java.util.List;

/**
 * PCAP 导入管理服务接口
 * 负责管理离线 PCAP 文件的导入任务，包括服务发现、任务分配、状态管理等核心功能
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface PcapImportManagerService {

    /**
     * 处理 PCAP 导入请求
     * 
     * @param request 导入请求参数
     * @return 导入结果
     */
    PcapImportResultDto processImportRequest(PcapImportRequestDto request);

    /**
     * 查找空闲的导入服务实例
     * 
     * @return 空闲服务实例ID，如果没有空闲实例则返回null
     */
    Integer findIdleService();

    /**
     * 获取所有导入服务的状态信息
     * 
     * @return 服务状态列表
     */
    List<ImportServiceStatusVo> getAllServiceStatus();

    /**
     * 检查指定服务实例的状态
     * 
     * @param serviceId 服务实例ID
     * @return 服务状态信息
     */
    ImportServiceStatusVo getServiceStatus(Integer serviceId);

    /**
     * 启动指定的导入服务实例
     * 
     * @param serviceId 服务实例ID
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @return 启动是否成功
     */
    boolean startImportService(Integer serviceId, Integer taskId, Integer batchId);

    /**
     * 停止指定的导入服务实例
     * 
     * @param serviceId 服务实例ID
     * @return 停止是否成功
     */
    boolean stopImportService(Integer serviceId);

    /**
     * 获取当前正在运行的导入任务数量
     * 
     * @return 运行中的任务数量
     */
    int getRunningTaskCount();

    /**
     * 获取最大并发导入任务数量
     * 
     * @return 最大并发数
     */
    int getMaxConcurrentTasks();
}
