package com.geeksec.task.application.service.impl;

import com.geeksec.task.application.service.TaskBatchManagerService;
import com.geeksec.task.model.dto.BatchOperationResultDto;
import com.geeksec.task.model.entity.TaskBatch;
import com.geeksec.task.repository.TaskBatchRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 任务批次管理服务实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskBatchManagerServiceImpl implements TaskBatchManagerService {

    private final TaskBatchRepository taskBatchRepository;
    private final JdbcTemplate jdbcTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchPcapCount(Integer batchId, Integer pcapCount) {
        try {
            String sql = "UPDATE tb_task_batch SET pcap_num = ? WHERE batch_id = ?";
            int updatedRows = jdbcTemplate.update(sql, pcapCount, batchId);
            
            if (updatedRows > 0) {
                log.info("更新批次PCAP文件数量成功，批次ID: {}, 文件数量: {}", batchId, pcapCount);
                return true;
            } else {
                log.warn("更新批次PCAP文件数量失败，批次不存在: {}", batchId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新批次PCAP文件数量失败，批次ID: {}", batchId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertBatchOfflineService(Integer taskId, Integer batchId, Integer serviceId, String serviceName) {
        try {
            String sql = "INSERT INTO tb_batch_offline_thd (task_id, batch_id, service_id, service_name) VALUES (?, ?, ?, ?)";
            int insertedRows = jdbcTemplate.update(sql, taskId, batchId, serviceId, serviceName);
            
            if (insertedRows > 0) {
                log.info("记录批次离线服务关联成功，任务ID: {}, 批次ID: {}, 服务ID: {}", taskId, batchId, serviceId);
                return true;
            } else {
                log.warn("记录批次离线服务关联失败");
                return false;
            }
        } catch (Exception e) {
            log.error("记录批次离线服务关联失败，任务ID: {}, 批次ID: {}, 服务ID: {}", taskId, batchId, serviceId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchStatus(Integer batchId, Integer status) {
        try {
            TaskBatch taskBatch = new TaskBatch();
            taskBatch.setBatchId(batchId);
            taskBatch.setBatchStatus(status);
            
            boolean updated = taskBatchRepository.updateById(taskBatch);
            
            if (updated) {
                log.info("更新批次状态成功，批次ID: {}, 状态: {}", batchId, status);
            } else {
                log.warn("更新批次状态失败，批次ID: {}", batchId);
            }
            
            return updated;
        } catch (Exception e) {
            log.error("更新批次状态失败，批次ID: {}, 状态: {}", batchId, status, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchProgress(Integer batchId, Double progress) {
        try {
            return taskBatchRepository.updateProgress(batchId, progress);
        } catch (Exception e) {
            log.error("更新批次进度失败，批次ID: {}, 进度: {}", batchId, progress, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchStatistics(Integer batchId, Long batchBytes, Integer batchSession, Integer batchAlarm) {
        try {
            TaskBatch taskBatch = new TaskBatch();
            taskBatch.setBatchId(batchId);
            taskBatch.setBatchBytes(batchBytes);
            taskBatch.setBatchSession(batchSession);
            taskBatch.setBatchAlarm(batchAlarm);
            
            boolean updated = taskBatchRepository.updateById(taskBatch);
            
            if (updated) {
                log.info("更新批次统计信息成功，批次ID: {}, 数据量: {}, 会话量: {}, 告警量: {}", 
                    batchId, batchBytes, batchSession, batchAlarm);
            } else {
                log.warn("更新批次统计信息失败，批次ID: {}", batchId);
            }
            
            return updated;
        } catch (Exception e) {
            log.error("更新批次统计信息失败，批次ID: {}", batchId, e);
            return false;
        }
    }

    @Override
    public TaskBatch getBatchInfo(Integer batchId) {
        try {
            return taskBatchRepository.getById(batchId);
        } catch (Exception e) {
            log.error("获取批次信息失败，批次ID: {}", batchId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchOperationResultDto createBatch(Integer taskId, String batchRemark, 
                                              String fullflowState, String flowlogState) {
        try {
            TaskBatch taskBatch = new TaskBatch();
            taskBatch.setTaskId(taskId);
            taskBatch.setTaskType(2); // 离线任务
            taskBatch.setBatchType(1); // 服务器数据
            taskBatch.setBatchRemark(batchRemark);
            taskBatch.setFullflowState(fullflowState);
            taskBatch.setFlowlogState(flowlogState);
            taskBatch.setBatchStatus(1); // 等待导入
            taskBatch.setBatchProgress(0.0);
            taskBatch.setBeginTime(LocalDateTime.now());
            taskBatch.setState(1); // 正在运行
            
            taskBatchRepository.save(taskBatch);
            
            log.info("创建批次成功，任务ID: {}, 批次ID: {}", taskId, taskBatch.getBatchId());
            return BatchOperationResultDto.success(taskBatch.getBatchId(), taskId, "CREATE", "批次创建成功");
            
        } catch (Exception e) {
            log.error("创建批次失败，任务ID: {}", taskId, e);
            return BatchOperationResultDto.failure(null, taskId, "CREATE", "批次创建失败", e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchOperationResultDto deleteBatch(Integer batchId) {
        try {
            TaskBatch taskBatch = taskBatchRepository.getById(batchId);
            if (taskBatch == null) {
                return BatchOperationResultDto.failure(batchId, null, "DELETE", "批次不存在", "批次ID: " + batchId);
            }
            
            // 删除批次
            taskBatchRepository.removeById(batchId);
            
            // 删除关联的服务记录
            String sql = "DELETE FROM tb_batch_offline_thd WHERE batch_id = ?";
            jdbcTemplate.update(sql, batchId);
            
            log.info("删除批次成功，批次ID: {}", batchId);
            return BatchOperationResultDto.success(batchId, taskBatch.getTaskId(), "DELETE", "批次删除成功");
            
        } catch (Exception e) {
            log.error("删除批次失败，批次ID: {}", batchId, e);
            return BatchOperationResultDto.failure(batchId, null, "DELETE", "批次删除失败", e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchOperationResultDto stopBatch(Integer batchId) {
        try {
            TaskBatch taskBatch = taskBatchRepository.getById(batchId);
            if (taskBatch == null) {
                return BatchOperationResultDto.failure(batchId, null, "STOP", "批次不存在", "批次ID: " + batchId);
            }
            
            // 更新批次状态为停止
            taskBatch.setState(0);
            taskBatch.setEndTime(LocalDateTime.now());
            taskBatchRepository.updateById(taskBatch);
            
            log.info("停止批次成功，批次ID: {}", batchId);
            return BatchOperationResultDto.success(batchId, taskBatch.getTaskId(), "STOP", "批次停止成功");
            
        } catch (Exception e) {
            log.error("停止批次失败，批次ID: {}", batchId, e);
            return BatchOperationResultDto.failure(batchId, null, "STOP", "批次停止失败", e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchOperationResultDto restartBatch(Integer batchId) {
        try {
            TaskBatch taskBatch = taskBatchRepository.getById(batchId);
            if (taskBatch == null) {
                return BatchOperationResultDto.failure(batchId, null, "RESTART", "批次不存在", "批次ID: " + batchId);
            }
            
            // 重启批次
            taskBatch.setState(1);
            taskBatch.setBatchStatus(2); // 正在导入
            taskBatch.setBeginTime(LocalDateTime.now());
            taskBatch.setEndTime(null);
            taskBatchRepository.updateById(taskBatch);
            
            log.info("重启批次成功，批次ID: {}", batchId);
            return BatchOperationResultDto.success(batchId, taskBatch.getTaskId(), "RESTART", "批次重启成功");
            
        } catch (Exception e) {
            log.error("重启批次失败，批次ID: {}", batchId, e);
            return BatchOperationResultDto.failure(batchId, null, "RESTART", "批次重启失败", e.getMessage());
        }
    }

    @Override
    public boolean isBatchCompleted(Integer batchId) {
        try {
            TaskBatch taskBatch = taskBatchRepository.getById(batchId);
            return taskBatch != null && taskBatch.getBatchStatus() == 3; // 导入完成
        } catch (Exception e) {
            log.error("检查批次完成状态失败，批次ID: {}", batchId, e);
            return false;
        }
    }

    @Override
    public Double getBatchProgress(Integer batchId) {
        try {
            TaskBatch taskBatch = taskBatchRepository.getById(batchId);
            return taskBatch != null ? taskBatch.getBatchProgress() : 0.0;
        } catch (Exception e) {
            log.error("获取批次进度失败，批次ID: {}", batchId, e);
            return 0.0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setBatchError(Integer batchId, String errorMessage) {
        try {
            TaskBatch taskBatch = new TaskBatch();
            taskBatch.setBatchId(batchId);
            taskBatch.setBatchStatus(4); // 导入失败
            taskBatch.setState(0); // 关闭
            taskBatch.setEndTime(LocalDateTime.now());
            
            boolean updated = taskBatchRepository.updateById(taskBatch);
            
            if (updated) {
                log.warn("设置批次错误状态成功，批次ID: {}, 错误信息: {}", batchId, errorMessage);
            } else {
                log.error("设置批次错误状态失败，批次ID: {}", batchId);
            }
            
            return updated;
        } catch (Exception e) {
            log.error("设置批次错误状态失败，批次ID: {}", batchId, e);
            return false;
        }
    }
}
