package com.geeksec.task.application.service.impl;

import com.geeksec.task.application.service.ImportTaskScheduler;
import com.geeksec.task.application.service.PcapImportManagerService;
import com.geeksec.task.model.dto.PcapImportRequestDto;
import com.geeksec.task.model.dto.PcapImportResultDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 导入任务调度器实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ImportTaskSchedulerImpl implements ImportTaskScheduler {

    private final PcapImportManagerService pcapImportManagerService;

    /**
     * 任务队列最大容量
     */
    @Value("${nta.task.pcap-import.scheduler.max-queue-size:100}")
    private int maxQueueSize;

    /**
     * 是否启用任务调度
     */
    @Value("${nta.task.pcap-import.scheduler.enabled:true}")
    private boolean schedulerEnabled;

    /**
     * 任务队列
     */
    private BlockingQueue<PcapImportRequestDto> taskQueue;

    /**
     * 正在处理的任务
     */
    private final ConcurrentMap<String, PcapImportRequestDto> processingTasks = new ConcurrentHashMap<>();

    /**
     * 调度器运行状态
     */
    private final AtomicBoolean running = new AtomicBoolean(false);

    /**
     * 统计计数器
     */
    private final AtomicInteger completedTaskCount = new AtomicInteger(0);
    private final AtomicInteger failedTaskCount = new AtomicInteger(0);
    private final AtomicLong totalProcessedTasks = new AtomicLong(0);

    /**
     * 最后处理时间
     */
    private volatile String lastProcessTime = "";

    @PostConstruct
    public void init() {
        this.taskQueue = new LinkedBlockingQueue<>(maxQueueSize);
        if (schedulerEnabled) {
            startScheduler();
        }
        log.info("导入任务调度器初始化完成，队列容量: {}, 调度器启用: {}", maxQueueSize, schedulerEnabled);
    }

    @PreDestroy
    public void destroy() {
        stopScheduler();
        log.info("导入任务调度器已停止");
    }

    @Override
    public PcapImportResultDto submitImportTask(PcapImportRequestDto request) {
        if (!running.get()) {
            return PcapImportResultDto.failure("任务调度器未运行", "请先启动任务调度器");
        }

        try {
            // 检查队列是否已满
            if (taskQueue.remainingCapacity() == 0) {
                return PcapImportResultDto.failure("任务队列已满", 
                    "当前队列大小: " + taskQueue.size() + ", 最大容量: " + maxQueueSize);
            }

            // 添加到队列
            boolean added = taskQueue.offer(request);
            if (added) {
                log.info("任务已提交到队列，任务ID: {}, 批次ID: {}, 队列大小: {}", 
                    request.getTaskId(), request.getBatchId(), taskQueue.size());
                
                PcapImportResultDto result = PcapImportResultDto.success(null, "任务调度器", 
                    request.getTaskId(), request.getBatchId(), request.getInputPaths());
                result.setMessage("任务已提交到调度队列");
                return result;
            } else {
                return PcapImportResultDto.failure("提交任务失败", "无法添加到任务队列");
            }

        } catch (Exception e) {
            log.error("提交导入任务失败", e);
            return PcapImportResultDto.failure("提交任务失败", e.getMessage());
        }
    }

    @Override
    public int getPendingTaskCount() {
        return taskQueue.size();
    }

    @Override
    public int getProcessingTaskCount() {
        return processingTasks.size();
    }

    @Override
    public void startScheduler() {
        if (running.compareAndSet(false, true)) {
            log.info("启动导入任务调度器");
        }
    }

    @Override
    public void stopScheduler() {
        if (running.compareAndSet(true, false)) {
            log.info("停止导入任务调度器");
            // 清理正在处理的任务
            processingTasks.clear();
        }
    }

    @Override
    public boolean isRunning() {
        return running.get();
    }

    @Override
    public SchedulerStatus getSchedulerStatus() {
        return new SchedulerStatus(
            running.get(),
            taskQueue.size(),
            processingTasks.size(),
            completedTaskCount.get(),
            failedTaskCount.get(),
            totalProcessedTasks.get()
        );
    }

    @Override
    public boolean cancelImportTask(Integer taskId, Integer batchId) {
        try {
            String taskKey = generateTaskKey(taskId, batchId);
            
            // 从正在处理的任务中移除
            PcapImportRequestDto removedTask = processingTasks.remove(taskKey);
            if (removedTask != null) {
                log.info("取消正在处理的任务，任务ID: {}, 批次ID: {}", taskId, batchId);
                return true;
            }

            // 从队列中移除
            boolean removed = taskQueue.removeIf(task -> 
                task.getTaskId().equals(taskId) && task.getBatchId().equals(batchId));
            
            if (removed) {
                log.info("取消队列中的任务，任务ID: {}, 批次ID: {}", taskId, batchId);
                return true;
            }

            log.warn("未找到要取消的任务，任务ID: {}, 批次ID: {}", taskId, batchId);
            return false;

        } catch (Exception e) {
            log.error("取消导入任务失败，任务ID: {}, 批次ID: {}", taskId, batchId, e);
            return false;
        }
    }

    @Override
    public List<PcapImportRequestDto> getPendingTasks() {
        return new ArrayList<>(taskQueue);
    }

    @Override
    public int clearTaskQueue() {
        int size = taskQueue.size();
        taskQueue.clear();
        log.info("清空任务队列，清空任务数量: {}", size);
        return size;
    }

    /**
     * 定时处理任务队列
     */
    @Scheduled(fixedDelay = 5000) // 每5秒执行一次
    public void processTaskQueue() {
        if (!running.get() || !schedulerEnabled) {
            return;
        }

        try {
            // 检查是否有可用的服务实例
            int runningTaskCount = pcapImportManagerService.getRunningTaskCount();
            int maxConcurrentTasks = pcapImportManagerService.getMaxConcurrentTasks();
            
            if (runningTaskCount >= maxConcurrentTasks) {
                log.debug("所有服务实例都在忙碌中，等待下次调度");
                return;
            }

            // 处理队列中的任务
            int availableSlots = maxConcurrentTasks - runningTaskCount;
            for (int i = 0; i < availableSlots && !taskQueue.isEmpty(); i++) {
                PcapImportRequestDto task = taskQueue.poll();
                if (task != null) {
                    processTask(task);
                }
            }

        } catch (Exception e) {
            log.error("处理任务队列失败", e);
        }
    }

    /**
     * 处理单个任务
     */
    private void processTask(PcapImportRequestDto task) {
        String taskKey = generateTaskKey(task.getTaskId(), task.getBatchId());
        
        try {
            // 添加到正在处理的任务列表
            processingTasks.put(taskKey, task);
            
            log.info("开始处理任务，任务ID: {}, 批次ID: {}", task.getTaskId(), task.getBatchId());
            
            // 调用导入管理服务处理任务
            PcapImportResultDto result = pcapImportManagerService.processImportRequest(task);
            
            // 更新统计信息
            totalProcessedTasks.incrementAndGet();
            lastProcessTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            
            if (result.getSuccess()) {
                completedTaskCount.incrementAndGet();
                log.info("任务处理成功，任务ID: {}, 批次ID: {}", task.getTaskId(), task.getBatchId());
            } else {
                failedTaskCount.incrementAndGet();
                log.error("任务处理失败，任务ID: {}, 批次ID: {}, 错误: {}", 
                    task.getTaskId(), task.getBatchId(), result.getMessage());
            }

        } catch (Exception e) {
            failedTaskCount.incrementAndGet();
            totalProcessedTasks.incrementAndGet();
            log.error("处理任务异常，任务ID: {}, 批次ID: {}", task.getTaskId(), task.getBatchId(), e);
        } finally {
            // 从正在处理的任务列表中移除
            processingTasks.remove(taskKey);
        }
    }

    /**
     * 生成任务键
     */
    private String generateTaskKey(Integer taskId, Integer batchId) {
        return taskId + "_" + batchId;
    }
}
