package com.geeksec.task.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * PCAP 导入请求 DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PcapImportRequestDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @NotNull(message = "任务ID不能为空")
    private Integer taskId;

    /**
     * 批次ID
     */
    @NotNull(message = "批次ID不能为空")
    private Integer batchId;

    /**
     * 输入目录或文件路径列表（MinIO中的路径）
     */
    @NotEmpty(message = "输入路径列表不能为空")
    private List<String> inputPaths;

    /**
     * 批次描述
     */
    private String batchDescription;

    /**
     * 全流量留存状态 (ON: 启用, OFF: 停用)
     */
    private String fullflowState = "OFF";

    /**
     * 流日志状态 (ON: 启用, OFF: 停用)
     */
    private String flowlogState = "OFF";

    /**
     * 是否强制重新处理
     */
    private Boolean forceReprocess = false;

    /**
     * 优先级 (1-10, 数字越大优先级越高)
     */
    private Integer priority = 5;
}
