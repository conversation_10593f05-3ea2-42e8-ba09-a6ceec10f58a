package com.geeksec.task.application.service.impl;

import com.geeksec.task.application.service.PcapImportManagerService;
import com.geeksec.task.application.service.PcapFileProcessorService;
import com.geeksec.task.application.service.RuleSyncService;
import com.geeksec.task.application.service.ServiceStatusManager;
import com.geeksec.task.application.service.TaskBatchManagerService;
import com.geeksec.task.model.dto.PcapImportRequestDto;
import com.geeksec.task.model.dto.PcapImportResultDto;
import com.geeksec.task.model.vo.ImportServiceStatusVo;
import com.geeksec.task.model.vo.ImportServiceStatusVo.ServiceStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * PCAP 导入管理服务实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PcapImportManagerServiceImpl implements PcapImportManagerService {

    private final ServiceStatusManager serviceStatusManager;
    private final PcapFileProcessorService pcapFileProcessorService;
    private final RuleSyncService ruleSyncService;
    private final TaskBatchManagerService taskBatchManagerService;

    /**
     * 最大并发导入任务数量
     */
    @Value("${nta.task.pcap-import.max-concurrent-tasks:3}")
    private int maxConcurrentTasks;

    /**
     * 任务执行线程池
     */
    private final ExecutorService taskExecutor = Executors.newCachedThreadPool(r -> {
        Thread thread = new Thread(r, "pcap-import-task");
        thread.setDaemon(true);
        return thread;
    });

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PcapImportResultDto processImportRequest(PcapImportRequestDto request) {
        log.info("开始处理PCAP导入请求，任务ID: {}, 批次ID: {}", request.getTaskId(), request.getBatchId());

        try {
            // 1. 查找空闲的服务实例
            Integer serviceId = findIdleService();
            if (serviceId == null) {
                return PcapImportResultDto.failure("没有可用的空闲服务实例", "所有服务实例都在忙碌中");
            }

            String serviceName = generateServiceName(serviceId);
            log.info("分配服务实例: {} (ID: {})", serviceName, serviceId);

            // 2. 发现和验证PCAP文件
            List<String> pcapFiles = pcapFileProcessorService.findPcapFiles(request.getInputPaths());
            if (pcapFiles.isEmpty()) {
                return PcapImportResultDto.failure("未找到有效的PCAP文件", 
                    "在指定路径中未发现.pcap、.cap或.pcapng文件");
            }

            log.info("发现PCAP文件数量: {}", pcapFiles.size());

            // 3. 更新批次信息
            taskBatchManagerService.updateBatchPcapCount(request.getBatchId(), pcapFiles.size());

            // 4. 同步规则和配置
            ruleSyncService.syncAllRules(request.getTaskId(), request.getBatchId(), serviceId);

            // 5. 启动导入服务
            boolean started = startImportService(serviceId, request.getTaskId(), request.getBatchId());
            if (!started) {
                return PcapImportResultDto.failure("启动导入服务失败", 
                    "服务实例 " + serviceName + " 启动失败");
            }

            // 6. 异步执行导入任务
            CompletableFuture.runAsync(() -> {
                executeImportTask(serviceId, request.getTaskId(), request.getBatchId(), pcapFiles);
            }, taskExecutor);

            // 7. 记录任务批次和服务关联
            taskBatchManagerService.insertBatchOfflineService(
                request.getTaskId(), request.getBatchId(), serviceId, serviceName);

            return PcapImportResultDto.success(serviceId, serviceName, 
                request.getTaskId(), request.getBatchId(), pcapFiles);

        } catch (Exception e) {
            log.error("处理PCAP导入请求失败", e);
            return PcapImportResultDto.failure("处理导入请求失败", e.getMessage());
        }
    }

    @Override
    public Integer findIdleService() {
        List<ImportServiceStatusVo> allStatus = serviceStatusManager.getAllServiceStatus();
        
        for (ImportServiceStatusVo status : allStatus) {
            if (status.getStatus() == ServiceStatus.IDLE) {
                log.debug("找到空闲服务实例: {}", status.getServiceId());
                return status.getServiceId();
            }
        }

        // 如果没有空闲服务，尝试创建新的服务实例
        for (int i = 1; i <= maxConcurrentTasks; i++) {
            ImportServiceStatusVo status = serviceStatusManager.getServiceStatus(i);
            if (status == null || status.getStatus() == ServiceStatus.STOPPED) {
                // 创建或重启服务实例
                if (serviceStatusManager.createOrRestartService(i)) {
                    log.info("创建新的服务实例: {}", i);
                    return i;
                }
            }
        }

        log.warn("没有可用的空闲服务实例");
        return null;
    }

    @Override
    public List<ImportServiceStatusVo> getAllServiceStatus() {
        return serviceStatusManager.getAllServiceStatus();
    }

    @Override
    public ImportServiceStatusVo getServiceStatus(Integer serviceId) {
        return serviceStatusManager.getServiceStatus(serviceId);
    }

    @Override
    public boolean startImportService(Integer serviceId, Integer taskId, Integer batchId) {
        return serviceStatusManager.startService(serviceId, taskId, batchId);
    }

    @Override
    public boolean stopImportService(Integer serviceId) {
        return serviceStatusManager.stopService(serviceId);
    }

    @Override
    public int getRunningTaskCount() {
        return (int) serviceStatusManager.getAllServiceStatus().stream()
            .filter(status -> status.getStatus() == ServiceStatus.ACTIVE)
            .count();
    }

    @Override
    public int getMaxConcurrentTasks() {
        return maxConcurrentTasks;
    }

    /**
     * 生成服务名称
     */
    private String generateServiceName(Integer serviceId) {
        return String.format("offline_pcap_import.%d.service", serviceId);
    }

    /**
     * 执行导入任务
     */
    private void executeImportTask(Integer serviceId, Integer taskId, Integer batchId, List<String> pcapFiles) {
        try {
            log.info("开始执行导入任务，服务ID: {}, 任务ID: {}, 批次ID: {}", serviceId, taskId, batchId);
            
            // 更新服务状态为活跃
            serviceStatusManager.updateServiceStatus(serviceId, ServiceStatus.ACTIVE, taskId, batchId);
            
            // 处理PCAP文件
            pcapFileProcessorService.processFiles(serviceId, taskId, batchId, pcapFiles);
            
            log.info("导入任务执行完成，服务ID: {}", serviceId);
            
        } catch (Exception e) {
            log.error("执行导入任务失败，服务ID: {}", serviceId, e);
            serviceStatusManager.updateServiceStatus(serviceId, ServiceStatus.ERROR, null, null);
        } finally {
            // 任务完成后将服务状态设置为空闲
            serviceStatusManager.updateServiceStatus(serviceId, ServiceStatus.IDLE, null, null);
        }
    }
}
