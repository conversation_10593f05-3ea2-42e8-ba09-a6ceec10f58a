package com.geeksec.task.application.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.geeksec.task.application.service.RuleSyncService;
import com.geeksec.task.model.dto.RuleSyncResultDto;
import com.geeksec.task.repository.TaskBatchRepository;
import com.geeksec.task.model.entity.TaskBatch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * 规则同步服务实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RuleSyncServiceImpl implements RuleSyncService {

    private final JdbcTemplate jdbcTemplate;
    private final TaskBatchRepository taskBatchRepository;

    @Value("${nta.task.pcap-import.rule-config.base-path:/tmp/nta/rule-config}")
    private String ruleConfigBasePath;

    @Override
    public RuleSyncResultDto syncAllRules(Integer taskId, Integer batchId, Integer serviceId) {
        log.info("开始同步所有规则，任务ID: {}, 批次ID: {}, 服务ID: {}", taskId, batchId, serviceId);
        
        try {
            LocalDateTime startTime = LocalDateTime.now();
            List<String> configFilePaths = new ArrayList<>();

            // 1. 同步特征规则
            RuleSyncResultDto featureResult = syncFeatureRules(taskId, batchId, serviceId);
            if (!featureResult.getSuccess()) {
                return featureResult;
            }
            if (featureResult.getConfigFilePaths() != null) {
                configFilePaths.addAll(featureResult.getConfigFilePaths());
            }

            // 2. 同步过滤规则
            RuleSyncResultDto filterResult = syncFilterRules(taskId, serviceId);
            if (!filterResult.getSuccess()) {
                return filterResult;
            }
            if (filterResult.getConfigFilePaths() != null) {
                configFilePaths.addAll(filterResult.getConfigFilePaths());
            }

            // 3. 同步流配置
            RuleSyncResultDto flowResult = syncFlowConfigs(batchId, serviceId);
            if (!flowResult.getSuccess()) {
                return flowResult;
            }
            if (flowResult.getConfigFilePaths() != null) {
                configFilePaths.addAll(flowResult.getConfigFilePaths());
            }

            // 4. 创建成功结果
            RuleSyncResultDto result = RuleSyncResultDto.success(taskId, batchId, serviceId);
            result.setRuleCount(featureResult.getRuleCount());
            result.setFilterRuleCount(filterResult.getFilterRuleCount());
            result.setConfigFilePaths(configFilePaths);
            result.setFullflowState(flowResult.getFullflowState());
            result.setFlowlogState(flowResult.getFlowlogState());
            result.setSyncStartTime(startTime);
            result.setSyncEndTime(LocalDateTime.now());

            log.info("同步所有规则成功，任务ID: {}, 规则数: {}, 过滤规则数: {}", 
                taskId, result.getRuleCount(), result.getFilterRuleCount());
            return result;

        } catch (Exception e) {
            log.error("同步所有规则失败，任务ID: {}", taskId, e);
            return RuleSyncResultDto.failure(taskId, batchId, serviceId, 
                "同步规则失败", e.getMessage());
        }
    }

    @Override
    public RuleSyncResultDto syncFeatureRules(Integer taskId, Integer batchId, Integer serviceId) {
        log.info("开始同步特征规则，任务ID: {}, 批次ID: {}", taskId, batchId);
        
        try {
            // 查询特征规则
            String sql = """
                SELECT rule_id, rule_level, rule_name, rule_desc, rule_size, capture_mode, 
                       rule_json, created_time, pb_drop, pcap_drop, updated_time, rule_state,
                       lib_respond_open, lib_respond_lib, lib_respond_config, lib_respond_session_end,
                       lib_data_so, lib_data_conf, rule_type
                FROM tb_rule 
                WHERE rule_state = '生效' AND task_id = ?
                """;

            List<Map<String, Object>> rules = jdbcTemplate.queryForList(sql, taskId);
            log.info("查询到特征规则数量: {}", rules.size());

            if (rules.isEmpty()) {
                RuleSyncResultDto result = RuleSyncResultDto.success(taskId, batchId, serviceId);
                result.setRuleCount(0);
                return result;
            }

            // 生成规则配置文件
            String configFilePath = generateRuleConfigFile(taskId, batchId, serviceId);
            
            // 处理规则内容
            StringBuilder ruleContent = new StringBuilder();
            StringBuilder ruleTagContent = new StringBuilder();
            
            for (Map<String, Object> rule : rules) {
                processFeatureRule(rule, ruleContent, ruleTagContent, serviceId);
            }

            // 写入规则文件
            writeRuleFiles(serviceId, ruleContent.toString(), ruleTagContent.toString());

            RuleSyncResultDto result = RuleSyncResultDto.success(taskId, batchId, serviceId);
            result.setRuleCount(rules.size());
            result.setConfigFilePaths(List.of(configFilePath));
            return result;

        } catch (Exception e) {
            log.error("同步特征规则失败，任务ID: {}", taskId, e);
            return RuleSyncResultDto.failure(taskId, batchId, serviceId, 
                "同步特征规则失败", e.getMessage());
        }
    }

    @Override
    public RuleSyncResultDto syncFilterRules(Integer taskId, Integer serviceId) {
        log.info("开始同步过滤规则，任务ID: {}", taskId);
        
        try {
            // 查询过滤规则配置
            String filterSql = """
                SELECT b.id, b.ip, a.state, b.filter_json 
                FROM tb_filter_state a, tb_filter_config b 
                WHERE b.status = 1 AND a.task_id = b.task_id AND b.task_id = ?
                """;

            List<Map<String, Object>> filterRules = jdbcTemplate.queryForList(filterSql, taskId);
            
            // 查询过滤状态
            String stateSql = "SELECT state FROM tb_filter_state WHERE task_id = ?";
            List<Map<String, Object>> filterStates = jdbcTemplate.queryForList(stateSql, taskId);

            // 生成过滤配置
            JSONObject config = generateFilterConfig(filterRules, filterStates);
            
            // 写入配置文件
            String configFilePath = generateFilterConfigFile(taskId, serviceId);
            writeConfigFile(configFilePath, config.toJSONString());

            RuleSyncResultDto result = RuleSyncResultDto.success(taskId, null, serviceId);
            result.setFilterRuleCount(filterRules.size());
            result.setConfigFilePaths(List.of(configFilePath));
            return result;

        } catch (Exception e) {
            log.error("同步过滤规则失败，任务ID: {}", taskId, e);
            return RuleSyncResultDto.failure(taskId, null, serviceId, 
                "同步过滤规则失败", e.getMessage());
        }
    }

    @Override
    public RuleSyncResultDto syncFlowConfigs(Integer batchId, Integer serviceId) {
        log.info("开始同步流配置，批次ID: {}", batchId);
        
        try {
            // 查询批次配置
            TaskBatch taskBatch = taskBatchRepository.getById(batchId);
            if (taskBatch == null) {
                return RuleSyncResultDto.failure(null, batchId, serviceId, 
                    "批次不存在", "批次ID: " + batchId);
            }

            String fullflowState = taskBatch.getFullflowState();
            String flowlogState = taskBatch.getFlowlogState();

            // 生成流配置文件
            List<String> configPaths = generateFlowConfigFiles(serviceId, fullflowState, flowlogState);

            RuleSyncResultDto result = RuleSyncResultDto.success(null, batchId, serviceId);
            result.setFullflowState(fullflowState);
            result.setFlowlogState(flowlogState);
            result.setConfigFilePaths(configPaths);
            return result;

        } catch (Exception e) {
            log.error("同步流配置失败，批次ID: {}", batchId, e);
            return RuleSyncResultDto.failure(null, batchId, serviceId, 
                "同步流配置失败", e.getMessage());
        }
    }

    @Override
    public String generateRuleConfigFile(Integer taskId, Integer batchId, Integer serviceId) {
        Path serviceConfigDir = getServiceConfigDir(serviceId);
        return serviceConfigDir.resolve("JsonRule/BasicRule/UserRule/rule.json").toString();
    }

    @Override
    public String generateFilterConfigFile(Integer taskId, Integer serviceId) {
        Path serviceConfigDir = getServiceConfigDir(serviceId);
        return serviceConfigDir.resolve("Config/Config.txt").toString();
    }

    @Override
    public boolean validateRuleConfig(Integer taskId, Integer batchId) {
        try {
            // 验证规则配置的基本有效性
            String sql = "SELECT COUNT(*) FROM tb_rule WHERE rule_state = '生效' AND task_id = ?";
            Integer ruleCount = jdbcTemplate.queryForObject(sql, Integer.class, taskId);

            log.info("任务 {} 有效规则数量: {}", taskId, ruleCount);
            return ruleCount != null && ruleCount >= 0;

        } catch (Exception e) {
            log.error("验证规则配置失败，任务ID: {}", taskId, e);
            return false;
        }
    }

    /**
     * 处理特征规则
     */
    private void processFeatureRule(Map<String, Object> rule, StringBuilder ruleContent,
                                  StringBuilder ruleTagContent, Integer serviceId) {
        try {
            String ruleJsonStr = (String) rule.get("rule_json");
            if (!StringUtils.hasText(ruleJsonStr)) {
                return;
            }

            JSONObject ruleJson = JSON.parseObject(ruleJsonStr);
            ruleJson.put("Name", rule.get("rule_name"));

            // 创建规则标签
            JSONObject ruleTag = new JSONObject();
            ruleTag.put("RuleId", rule.get("rule_id"));
            ruleTag.put("rule_level", rule.get("rule_level"));
            ruleTag.put("RuleName", rule.get("rule_name"));
            ruleTag.put("Tag", new ArrayList<>());
            ruleTag.put("Infor", rule.get("rule_desc"));
            ruleTag.put("rule_size", rule.get("rule_size"));
            ruleTag.put("capture_mode", rule.get("capture_mode"));
            ruleTag.put("created_time", rule.get("created_time"));
            ruleTag.put("PbDrop", rule.get("pb_drop"));
            ruleTag.put("PcapDrop", rule.get("pcap_drop"));
            ruleTag.put("updated_time", rule.get("updated_time"));

            // 处理动态库规则
            String ruleType = (String) rule.get("rule_type");
            if (StringUtils.hasText(ruleType)) {
                processLibraryRule(rule, ruleJson, ruleType, serviceId);
            }

            // 添加到内容中
            ruleTagContent.append("\n\n").append(ruleTag.toJSONString());
            ruleContent.append("\n\n").append(ruleJson.toJSONString().replace("\n", ""));

        } catch (Exception e) {
            log.error("处理特征规则失败，规则ID: {}", rule.get("rule_id"), e);
        }
    }

    /**
     * 处理动态库规则
     */
    private void processLibraryRule(Map<String, Object> rule, JSONObject ruleJson,
                                  String ruleType, Integer serviceId) {
        try {
            String[] ruleTypes = ruleType.split(",");

            for (String type : ruleTypes) {
                if ("6".equals(type.trim())) {
                    // 动态库规则类型6
                    processLibraryType6(rule, ruleJson, serviceId);
                } else if ("7".equals(type.trim())) {
                    // 动态库规则类型7
                    processLibraryType7(rule, ruleJson, serviceId);
                }
            }
        } catch (Exception e) {
            log.error("处理动态库规则失败，规则ID: {}", rule.get("rule_id"), e);
        }
    }

    /**
     * 处理动态库规则类型6
     */
    private void processLibraryType6(Map<String, Object> rule, JSONObject ruleJson, Integer serviceId) {
        try {
            String libName = (String) rule.get("lib_respond_lib");
            String libDataSo = (String) rule.get("lib_data_so");

            if (StringUtils.hasText(libName) && StringUtils.hasText(libDataSo)) {
                // 创建库文件目录
                Path libDir = getServiceConfigDir(serviceId).resolve("JsonRule/BasicRule/LibFolder");
                Files.createDirectories(libDir);

                // 写入动态库文件
                Path libFile = libDir.resolve(libName);
                byte[] libData = Base64.getDecoder().decode(libDataSo);
                Files.write(libFile, libData);

                // 处理配置文件
                String libConfig = (String) rule.get("lib_respund_config");
                String libDataConf = (String) rule.get("lib_data_conf");
                if (StringUtils.hasText(libConfig) && StringUtils.hasText(libDataConf)) {
                    Path confDir = getServiceConfigDir(serviceId)
                        .resolve("JsonRule/BasicRule/LibConfig/" + rule.get("rule_id"));
                    Files.createDirectories(confDir);

                    Path confFile = confDir.resolve(libConfig);
                    byte[] confData = Base64.getDecoder().decode(libDataConf);
                    Files.write(confFile, confData);
                }

                // 更新规则JSON
                JSONObject libRespond = ruleJson.getJSONObject("LibRespond");
                if (libRespond == null) {
                    libRespond = new JSONObject();
                    ruleJson.put("LibRespond", libRespond);
                }
                libRespond.put("Lib", libName);
            }
        } catch (Exception e) {
            log.error("处理动态库规则类型6失败，规则ID: {}", rule.get("rule_id"), e);
        }
    }

    /**
     * 处理动态库规则类型7
     */
    private void processLibraryType7(Map<String, Object> rule, JSONObject ruleJson, Integer serviceId) {
        try {
            Integer ruleId = (Integer) rule.get("rule_id");
            String libName = ruleId + ".so";

            // 生成配置文件
            JSONObject detailRespond = ruleJson.getJSONObject("DetailRespond");
            if (detailRespond != null) {
                Path tempConfFile = Paths.get("/tmp", ruleId + ".json");
                Files.write(tempConfFile, detailRespond.toJSONString().getBytes());

                // 创建库文件目录
                Path libDir = getServiceConfigDir(serviceId).resolve("JsonRule/BasicRule/LibFolder");
                Files.createDirectories(libDir);
                Path libFile = libDir.resolve(libName);

                // 这里应该调用动态库生成脚本，暂时跳过
                log.info("需要生成动态库文件: {}", libFile);

                // 更新规则JSON
                JSONObject libRespond = ruleJson.getJSONObject("LibRespond");
                if (libRespond == null) {
                    libRespond = new JSONObject();
                    ruleJson.put("LibRespond", libRespond);
                }
                libRespond.put("Lib", libName);

                // 清理临时文件
                Files.deleteIfExists(tempConfFile);
            }
        } catch (Exception e) {
            log.error("处理动态库规则类型7失败，规则ID: {}", rule.get("rule_id"), e);
        }
    }

    /**
     * 生成过滤配置
     */
    private JSONObject generateFilterConfig(List<Map<String, Object>> filterRules,
                                          List<Map<String, Object>> filterStates) {
        // 基础配置模板
        String configTemplate = """
            {
                "Basic": {
                    "DeviceNO": "123-456",
                    "Mission": "testn",
                    "OfflineFolder": "./OfflineFolder",
                    "FirstPro": 12
                },
                "PFRing": {
                    "Interface": ["wlp3s0"],
                    "ThreadNum": 2,
                    "RenewInterval_Sec": 120,
                    "CPU": [1, 2, 3, 4],
                    "BalanceCore": 11
                },
                "Connect": {
                    "IsIP": 1,
                    "IsUDP": 1,
                    "SYNSign": 0
                },
                "ConnectInfor": {
                    "ConnectNum_IPv4": 10000,
                    "ConnectNum_IPv6": 1232,
                    "TimeInterval": 90
                },
                "LanProbe": {
                    "FirstPro": [10, 12, 113, 277],
                    "SaveType_Pcap": 1,
                    "StartTime": "2018-01-19 16:51:00",
                    "LogFolder": "./FlowLog",
                    "RuleFolder": "./JsonRule/BasicRule",
                    "EngineFolder": "./JsonRule/BasicEngine",
                    "SaveFolder": "./PcapFolder",
                    "SaveFlow": 0,
                    "SaveFirstPacket": 1,
                    "RuleLog_Folder": "./FlowLog/RuleLog",
                    "RuleLog_MinLevel": 20
                }
            }
            """;

        JSONObject config = JSON.parseObject(configTemplate);

        // 处理过滤规则
        JSONObject filterConfig = new JSONObject();

        if (filterRules.isEmpty()) {
            // 没有过滤规则时使用默认状态
            String response = "pass";
            if (!filterStates.isEmpty()) {
                Integer state = (Integer) filterStates.get(0).get("state");
                response = (state == 0) ? "pass" : "drop";
            }
            filterConfig.put("Respond", response);
            filterConfig.put("Rule", new ArrayList<>());
        } else {
            // 有过滤规则时处理
            Integer state = (Integer) filterRules.get(0).get("state");
            String response = (state == 0) ? "pass" : "drop";
            filterConfig.put("Respond", response);

            List<JSONObject> rules = new ArrayList<>();
            for (Map<String, Object> rule : filterRules) {
                String filterJsonStr = (String) rule.get("filter_json");
                if (StringUtils.hasText(filterJsonStr)) {
                    JSONObject filterRule = JSON.parseObject(filterJsonStr);
                    filterRule.put("ID", rule.get("id"));

                    // 处理空IP字段
                    if (filterRule.containsKey("ip") && !StringUtils.hasText(filterRule.getString("ip"))) {
                        filterRule.remove("ip");
                    }

                    rules.add(filterRule);
                }
            }
            filterConfig.put("Rule", rules);
        }

        config.put("Filter", filterConfig);
        return config;
    }

    /**
     * 生成流配置文件
     */
    private List<String> generateFlowConfigFiles(Integer serviceId, String fullflowState, String flowlogState)
            throws IOException {
        List<String> configPaths = new ArrayList<>();
        Path serviceConfigDir = getServiceConfigDir(serviceId);

        // 1. 生成 write_pcap.xml 配置
        String xmlConfigPath = generateWritePcapXml(serviceConfigDir, fullflowState);
        configPaths.add(xmlConfigPath);

        // 2. 生成 plugin_conf.json 配置
        String pluginConfigPath = generatePluginConfig(serviceConfigDir, flowlogState);
        configPaths.add(pluginConfigPath);

        return configPaths;
    }

    /**
     * 生成 write_pcap.xml 配置文件
     */
    private String generateWritePcapXml(Path serviceConfigDir, String fullflowState) throws IOException {
        String xmlTemplate = """
            <?xml version="1.0" encoding="UTF-8"?>
            <config>
                <b_rule_save>%s</b_rule_save>
            </config>
            """;

        boolean shouldSave = "OFF".equals(fullflowState);
        String xmlContent = String.format(xmlTemplate, shouldSave ? "true" : "false");

        Path xmlFile = serviceConfigDir.resolve("write_pcap.xml");
        Files.createDirectories(xmlFile.getParent());
        Files.write(xmlFile, xmlContent.getBytes());

        return xmlFile.toString();
    }

    /**
     * 生成插件配置文件
     */
    private String generatePluginConfig(Path serviceConfigDir, String flowlogState) throws IOException {
        int shouldLogValue = "ON".equals(flowlogState) ? 1 : 0;

        JSONObject pluginConfig = new JSONObject();
        JSONObject protoParseConfig = new JSONObject();
        protoParseConfig.put("should_log_def", shouldLogValue);

        // 添加插件配置
        List<JSONObject> plugins = new ArrayList<>();
        JSONObject plugin = new JSONObject();
        plugin.put("should_log", shouldLogValue);
        plugins.add(plugin);
        protoParseConfig.put("plugin", plugins);

        pluginConfig.put("proto_parse", protoParseConfig);

        Path pluginFile = serviceConfigDir.resolve("plugin_conf.json");
        Files.createDirectories(pluginFile.getParent());
        Files.write(pluginFile, pluginConfig.toJSONString().getBytes());

        return pluginFile.toString();
    }

    /**
     * 写入规则文件
     */
    private void writeRuleFiles(Integer serviceId, String ruleContent, String ruleTagContent) throws IOException {
        Path serviceConfigDir = getServiceConfigDir(serviceId);

        // 写入规则文件
        Path ruleDir = serviceConfigDir.resolve("JsonRule/BasicRule/UserRule");
        Files.createDirectories(ruleDir);

        Path ruleFile = ruleDir.resolve("rule.json");
        Files.write(ruleFile, ruleContent.getBytes());

        // 写入规则标签文件
        Path ruleTagFile = serviceConfigDir.resolve("ruletag.json");
        Files.write(ruleTagFile, ruleTagContent.getBytes());
    }

    /**
     * 写入配置文件
     */
    private void writeConfigFile(String filePath, String content) throws IOException {
        Path file = Paths.get(filePath);
        Files.createDirectories(file.getParent());
        Files.write(file, content.getBytes());
    }

    /**
     * 获取服务配置目录
     */
    private Path getServiceConfigDir(Integer serviceId) {
        return Paths.get(ruleConfigBasePath, "service-" + serviceId);
    }
}
