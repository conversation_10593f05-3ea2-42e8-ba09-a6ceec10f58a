package com.geeksec.task.application.service.impl;

import com.geeksec.task.application.service.MinioFileService;
import com.geeksec.task.application.service.PcapFileProcessorService;
import com.geeksec.task.application.service.ServiceStatusManager;
import com.geeksec.task.model.vo.FileTreeNodeVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * PCAP 文件处理服务实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PcapFileProcessorServiceImpl implements PcapFileProcessorService {

    private final MinioFileService minioFileService;
    private final ServiceStatusManager serviceStatusManager;

    /**
     * 支持的PCAP文件扩展名
     */
    private static final List<String> PCAP_EXTENSIONS = Arrays.asList(".pcap", ".cap", ".pcapng");

    @Override
    public List<String> findPcapFiles(List<String> inputPaths) {
        log.info("开始查找PCAP文件，输入路径数量: {}", inputPaths.size());
        
        List<String> pcapFiles = new ArrayList<>();
        
        for (String inputPath : inputPaths) {
            try {
                if (isFile(inputPath)) {
                    // 如果是文件，检查是否为PCAP文件
                    if (isPcapFile(inputPath)) {
                        pcapFiles.add(inputPath);
                        log.debug("发现PCAP文件: {}", inputPath);
                    }
                } else {
                    // 如果是目录，递归查找PCAP文件
                    List<String> filesInDir = findPcapFilesInDirectory(inputPath);
                    pcapFiles.addAll(filesInDir);
                    log.debug("在目录 {} 中发现 {} 个PCAP文件", inputPath, filesInDir.size());
                }
            } catch (Exception e) {
                log.error("处理路径失败: {}", inputPath, e);
            }
        }
        
        log.info("总共发现PCAP文件数量: {}", pcapFiles.size());
        return pcapFiles;
    }

    @Override
    public boolean validatePcapFile(String filePath) {
        try {
            // 检查文件扩展名
            if (!isPcapFile(filePath)) {
                return false;
            }
            
            // 检查文件是否存在
            if (!fileExists(filePath)) {
                log.warn("PCAP文件不存在: {}", filePath);
                return false;
            }
            
            // 检查文件大小
            long fileSize = getFileSize(filePath);
            if (fileSize <= 0) {
                log.warn("PCAP文件大小无效: {}, 大小: {}", filePath, fileSize);
                return false;
            }
            
            // TODO: 可以添加更多的PCAP文件格式验证
            
            return true;
            
        } catch (Exception e) {
            log.error("验证PCAP文件失败: {}", filePath, e);
            return false;
        }
    }

    @Override
    public PcapFileInfo getPcapFileInfo(String filePath) {
        try {
            String fileName = extractFileName(filePath);
            long fileSize = getFileSize(filePath);
            boolean isValid = validatePcapFile(filePath);
            
            PcapFileInfo info = new PcapFileInfo(filePath, fileSize, fileName, isValid);
            
            if (!isValid) {
                info.setErrorMessage("文件验证失败");
            }
            
            return info;
            
        } catch (Exception e) {
            log.error("获取PCAP文件信息失败: {}", filePath, e);
            PcapFileInfo info = new PcapFileInfo(filePath, 0, extractFileName(filePath), false);
            info.setErrorMessage("获取文件信息失败: " + e.getMessage());
            return info;
        }
    }

    @Override
    public void processFiles(Integer serviceId, Integer taskId, Integer batchId, List<String> pcapFiles) {
        log.info("开始处理PCAP文件，服务ID: {}, 任务ID: {}, 批次ID: {}, 文件数量: {}", 
            serviceId, taskId, batchId, pcapFiles.size());
        
        try {
            int totalFiles = pcapFiles.size();
            int processedFiles = 0;
            
            for (String pcapFile : pcapFiles) {
                try {
                    // 处理单个PCAP文件
                    processSingleFile(serviceId, taskId, batchId, pcapFile);
                    processedFiles++;
                    
                    // 更新进度
                    double progress = (double) processedFiles / totalFiles;
                    serviceStatusManager.updateServiceProgress(serviceId, progress, processedFiles, totalFiles);
                    
                    log.debug("处理文件完成: {} ({}/{})", pcapFile, processedFiles, totalFiles);
                    
                } catch (Exception e) {
                    log.error("处理PCAP文件失败: {}", pcapFile, e);
                    // 继续处理下一个文件
                }
            }
            
            log.info("PCAP文件处理完成，服务ID: {}, 处理文件数: {}/{}", serviceId, processedFiles, totalFiles);
            
        } catch (Exception e) {
            log.error("处理PCAP文件列表失败，服务ID: {}", serviceId, e);
            serviceStatusManager.setServiceError(serviceId, "处理PCAP文件失败: " + e.getMessage());
        }
    }

    @Override
    public FileExistenceCheckResult checkFileExistence(List<String> filePaths) {
        List<String> existingFiles = new ArrayList<>();
        List<String> nonExistingFiles = new ArrayList<>();
        
        for (String filePath : filePaths) {
            if (fileExists(filePath)) {
                existingFiles.add(filePath);
            } else {
                nonExistingFiles.add(filePath);
            }
        }
        
        return new FileExistenceCheckResult(existingFiles, nonExistingFiles);
    }

    @Override
    public long getFileSize(String filePath) {
        try {
            // 通过MinIO获取文件大小
            return minioFileService.getFileSize(filePath);
        } catch (Exception e) {
            log.error("获取文件大小失败: {}", filePath, e);
            return 0L;
        }
    }

    /**
     * 检查路径是否为文件
     */
    private boolean isFile(String path) {
        // 简单的判断逻辑：如果路径包含文件扩展名，则认为是文件
        return PCAP_EXTENSIONS.stream().anyMatch(ext -> path.toLowerCase().endsWith(ext));
    }

    /**
     * 检查是否为PCAP文件
     */
    private boolean isPcapFile(String filePath) {
        String lowerPath = filePath.toLowerCase();
        return PCAP_EXTENSIONS.stream().anyMatch(lowerPath::endsWith);
    }

    /**
     * 在目录中查找PCAP文件
     */
    private List<String> findPcapFilesInDirectory(String directoryPath) {
        try {
            List<FileTreeNodeVo> files = minioFileService.listServerPath(directoryPath);
            return files.stream()
                .filter(file -> !file.getDisabled() && isPcapFile(file.getFilePath()))
                .map(FileTreeNodeVo::getFilePath)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("在目录中查找PCAP文件失败: {}", directoryPath, e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查文件是否存在
     */
    private boolean fileExists(String filePath) {
        try {
            // 通过MinIO检查文件是否存在
            MinioFileService.CheckFilePathsResult result = minioFileService.checkFilePaths(List.of(filePath));
            return result.isStatus() && result.getNonExistFiles().isEmpty();
        } catch (Exception e) {
            log.error("检查文件存在性失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 处理单个PCAP文件
     */
    private void processSingleFile(Integer serviceId, Integer taskId, Integer batchId, String pcapFile) {
        log.debug("处理单个PCAP文件: {}", pcapFile);
        
        // 验证文件
        if (!validatePcapFile(pcapFile)) {
            log.warn("PCAP文件验证失败，跳过处理: {}", pcapFile);
            return;
        }
        
        // TODO: 这里应该调用实际的PCAP文件处理逻辑
        // 例如：启动流量分析引擎处理该文件
        
        // 模拟处理时间
        try {
            Thread.sleep(100); // 模拟处理时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 从路径中提取文件名
     */
    private String extractFileName(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "";
        }
        
        int lastSlashIndex = filePath.lastIndexOf('/');
        if (lastSlashIndex >= 0 && lastSlashIndex < filePath.length() - 1) {
            return filePath.substring(lastSlashIndex + 1);
        }
        
        return filePath;
    }
}
