package com.geeksec.task.application.service.impl;

import com.geeksec.task.application.service.ServiceStatusManager;
import com.geeksec.task.model.vo.ImportServiceStatusVo;
import com.geeksec.task.model.vo.ImportServiceStatusVo.ServiceStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 服务状态管理器实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
public class ServiceStatusManagerImpl implements ServiceStatusManager {

    /**
     * 最大并发服务数量
     */
    @Value("${nta.task.pcap-import.max-concurrent-tasks:3}")
    private int maxConcurrentTasks;

    /**
     * 服务状态缓存
     */
    private final ConcurrentMap<Integer, ImportServiceStatusVo> serviceStatusMap = new ConcurrentHashMap<>();

    @Override
    public List<ImportServiceStatusVo> getAllServiceStatus() {
        // 确保所有服务实例都已初始化
        initializeAllServices();
        return new ArrayList<>(serviceStatusMap.values());
    }

    @Override
    public ImportServiceStatusVo getServiceStatus(Integer serviceId) {
        if (serviceId == null || serviceId < 1 || serviceId > maxConcurrentTasks) {
            return null;
        }
        
        return serviceStatusMap.computeIfAbsent(serviceId, id -> {
            String serviceName = generateServiceName(id);
            return ImportServiceStatusVo.createIdleService(id, serviceName);
        });
    }

    @Override
    public boolean updateServiceStatus(Integer serviceId, ServiceStatus status, Integer taskId, Integer batchId) {
        try {
            ImportServiceStatusVo serviceStatus = getServiceStatus(serviceId);
            if (serviceStatus == null) {
                log.warn("服务实例不存在: {}", serviceId);
                return false;
            }

            serviceStatus.setStatus(status);
            serviceStatus.setCurrentTaskId(taskId);
            serviceStatus.setCurrentBatchId(batchId);
            serviceStatus.setLastUpdateTime(LocalDateTime.now());

            if (status == ServiceStatus.ACTIVE && taskId != null) {
                serviceStatus.setTaskStartTime(LocalDateTime.now());
                serviceStatus.setProgress(0.0);
                serviceStatus.setProcessedFileCount(0);
                serviceStatus.setErrorMessage(null);
            } else if (status == ServiceStatus.IDLE) {
                serviceStatus.setCurrentTaskId(null);
                serviceStatus.setCurrentBatchId(null);
                serviceStatus.setTaskStartTime(null);
                serviceStatus.setProgress(0.0);
                serviceStatus.setProcessedFileCount(0);
                serviceStatus.setTotalFileCount(0);
                serviceStatus.setErrorMessage(null);
            }

            log.debug("更新服务状态成功，服务ID: {}, 状态: {}", serviceId, status);
            return true;

        } catch (Exception e) {
            log.error("更新服务状态失败，服务ID: {}", serviceId, e);
            return false;
        }
    }

    @Override
    public boolean startService(Integer serviceId, Integer taskId, Integer batchId) {
        try {
            ImportServiceStatusVo serviceStatus = getServiceStatus(serviceId);
            if (serviceStatus == null) {
                log.warn("服务实例不存在: {}", serviceId);
                return false;
            }

            if (serviceStatus.getStatus() != ServiceStatus.IDLE) {
                log.warn("服务实例不是空闲状态，无法启动: {}, 当前状态: {}", serviceId, serviceStatus.getStatus());
                return false;
            }

            return updateServiceStatus(serviceId, ServiceStatus.ACTIVE, taskId, batchId);

        } catch (Exception e) {
            log.error("启动服务失败，服务ID: {}", serviceId, e);
            return false;
        }
    }

    @Override
    public boolean stopService(Integer serviceId) {
        try {
            ImportServiceStatusVo serviceStatus = getServiceStatus(serviceId);
            if (serviceStatus == null) {
                log.warn("服务实例不存在: {}", serviceId);
                return false;
            }

            return updateServiceStatus(serviceId, ServiceStatus.STOPPED, null, null);

        } catch (Exception e) {
            log.error("停止服务失败，服务ID: {}", serviceId, e);
            return false;
        }
    }

    @Override
    public boolean createOrRestartService(Integer serviceId) {
        try {
            if (serviceId == null || serviceId < 1 || serviceId > maxConcurrentTasks) {
                log.warn("无效的服务ID: {}", serviceId);
                return false;
            }

            String serviceName = generateServiceName(serviceId);
            ImportServiceStatusVo serviceStatus = ImportServiceStatusVo.createIdleService(serviceId, serviceName);
            serviceStatusMap.put(serviceId, serviceStatus);

            log.info("创建或重启服务成功，服务ID: {}, 服务名称: {}", serviceId, serviceName);
            return true;

        } catch (Exception e) {
            log.error("创建或重启服务失败，服务ID: {}", serviceId, e);
            return false;
        }
    }

    @Override
    public boolean updateServiceProgress(Integer serviceId, Double progress, 
                                       Integer processedFileCount, Integer totalFileCount) {
        try {
            ImportServiceStatusVo serviceStatus = getServiceStatus(serviceId);
            if (serviceStatus == null) {
                log.warn("服务实例不存在: {}", serviceId);
                return false;
            }

            serviceStatus.setProgress(progress);
            serviceStatus.setProcessedFileCount(processedFileCount);
            serviceStatus.setTotalFileCount(totalFileCount);
            serviceStatus.setLastUpdateTime(LocalDateTime.now());

            log.debug("更新服务进度成功，服务ID: {}, 进度: {:.2f}%, 已处理: {}/{}", 
                serviceId, progress * 100, processedFileCount, totalFileCount);
            return true;

        } catch (Exception e) {
            log.error("更新服务进度失败，服务ID: {}", serviceId, e);
            return false;
        }
    }

    @Override
    public boolean setServiceError(Integer serviceId, String errorMessage) {
        try {
            ImportServiceStatusVo serviceStatus = getServiceStatus(serviceId);
            if (serviceStatus == null) {
                log.warn("服务实例不存在: {}", serviceId);
                return false;
            }

            serviceStatus.setStatus(ServiceStatus.ERROR);
            serviceStatus.setErrorMessage(errorMessage);
            serviceStatus.setLastUpdateTime(LocalDateTime.now());

            log.warn("设置服务错误状态，服务ID: {}, 错误信息: {}", serviceId, errorMessage);
            return true;

        } catch (Exception e) {
            log.error("设置服务错误状态失败，服务ID: {}", serviceId, e);
            return false;
        }
    }

    @Override
    public boolean isServiceHealthy(Integer serviceId) {
        ImportServiceStatusVo serviceStatus = getServiceStatus(serviceId);
        return serviceStatus != null && serviceStatus.getStatus() != ServiceStatus.ERROR;
    }

    @Override
    public int getIdleServiceCount() {
        return (int) serviceStatusMap.values().stream()
            .filter(status -> status.getStatus() == ServiceStatus.IDLE)
            .count();
    }

    @Override
    public int getActiveServiceCount() {
        return (int) serviceStatusMap.values().stream()
            .filter(status -> status.getStatus() == ServiceStatus.ACTIVE)
            .count();
    }

    /**
     * 初始化所有服务实例
     */
    private void initializeAllServices() {
        for (int i = 1; i <= maxConcurrentTasks; i++) {
            serviceStatusMap.computeIfAbsent(i, id -> {
                String serviceName = generateServiceName(id);
                return ImportServiceStatusVo.createIdleService(id, serviceName);
            });
        }
    }

    /**
     * 生成服务名称
     */
    private String generateServiceName(Integer serviceId) {
        return String.format("offline_pcap_import.%d.service", serviceId);
    }
}
