package com.geeksec.task.application.service;

import java.util.List;

/**
 * PCAP 文件处理服务接口
 * 用于处理 PCAP 文件的发现、验证和批次管理，集成 MinIO 存储
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface PcapFileProcessorService {

    /**
     * 查找PCAP文件
     * 
     * @param inputPaths 输入路径列表（MinIO中的路径）
     * @return PCAP文件路径列表
     */
    List<String> findPcapFiles(List<String> inputPaths);

    /**
     * 验证PCAP文件是否有效
     * 
     * @param filePath PCAP文件路径
     * @return 文件是否有效
     */
    boolean validatePcapFile(String filePath);

    /**
     * 获取PCAP文件信息
     * 
     * @param filePath PCAP文件路径
     * @return 文件信息
     */
    PcapFileInfo getPcapFileInfo(String filePath);

    /**
     * 处理PCAP文件列表
     * 
     * @param serviceId 服务实例ID
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @param pcapFiles PCAP文件路径列表
     */
    void processFiles(Integer serviceId, Integer taskId, Integer batchId, List<String> pcapFiles);

    /**
     * 检查文件路径是否存在
     * 
     * @param filePaths 文件路径列表
     * @return 检查结果
     */
    FileExistenceCheckResult checkFileExistence(List<String> filePaths);

    /**
     * 获取文件大小
     * 
     * @param filePath 文件路径
     * @return 文件大小（字节）
     */
    long getFileSize(String filePath);

    /**
     * PCAP文件信息
     */
    class PcapFileInfo {
        private String filePath;
        private long fileSize;
        private String fileName;
        private boolean isValid;
        private String errorMessage;

        // 构造函数
        public PcapFileInfo(String filePath, long fileSize, String fileName, boolean isValid) {
            this.filePath = filePath;
            this.fileSize = fileSize;
            this.fileName = fileName;
            this.isValid = isValid;
        }

        // Getters and Setters
        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }
        
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public boolean isValid() { return isValid; }
        public void setValid(boolean valid) { isValid = valid; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }

    /**
     * 文件存在性检查结果
     */
    class FileExistenceCheckResult {
        private List<String> existingFiles;
        private List<String> nonExistingFiles;
        private boolean allExist;

        public FileExistenceCheckResult(List<String> existingFiles, List<String> nonExistingFiles) {
            this.existingFiles = existingFiles;
            this.nonExistingFiles = nonExistingFiles;
            this.allExist = nonExistingFiles.isEmpty();
        }

        // Getters
        public List<String> getExistingFiles() { return existingFiles; }
        public List<String> getNonExistingFiles() { return nonExistingFiles; }
        public boolean isAllExist() { return allExist; }
    }
}
