package com.geeksec.task.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * PCAP 导入结果 DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PcapImportResultDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 导入是否成功
     */
    private Boolean success;

    /**
     * 结果消息
     */
    private String message;

    /**
     * 分配的服务实例ID
     */
    private Integer serviceId;

    /**
     * 服务实例名称
     */
    private String serviceName;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 批次ID
     */
    private Integer batchId;

    /**
     * 发现的PCAP文件数量
     */
    private Integer pcapFileCount;

    /**
     * 发现的PCAP文件路径列表
     */
    private List<String> pcapFilePaths;

    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 预估完成时间
     */
    private LocalDateTime estimatedEndTime;

    /**
     * 错误详情（如果有）
     */
    private String errorDetail;

    /**
     * 创建成功结果
     */
    public static PcapImportResultDto success(Integer serviceId, String serviceName, 
                                            Integer taskId, Integer batchId, 
                                            List<String> pcapFilePaths) {
        PcapImportResultDto result = new PcapImportResultDto();
        result.setSuccess(true);
        result.setMessage("PCAP导入任务创建成功");
        result.setServiceId(serviceId);
        result.setServiceName(serviceName);
        result.setTaskId(taskId);
        result.setBatchId(batchId);
        result.setPcapFilePaths(pcapFilePaths);
        result.setPcapFileCount(pcapFilePaths != null ? pcapFilePaths.size() : 0);
        result.setStartTime(LocalDateTime.now());
        return result;
    }

    /**
     * 创建失败结果
     */
    public static PcapImportResultDto failure(String message, String errorDetail) {
        PcapImportResultDto result = new PcapImportResultDto();
        result.setSuccess(false);
        result.setMessage(message);
        result.setErrorDetail(errorDetail);
        return result;
    }
}
