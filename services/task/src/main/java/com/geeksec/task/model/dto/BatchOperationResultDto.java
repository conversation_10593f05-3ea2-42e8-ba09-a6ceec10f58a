package com.geeksec.task.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 批次操作结果 DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BatchOperationResultDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 结果消息
     */
    private String message;

    /**
     * 批次ID
     */
    private Integer batchId;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 错误详情（如果有）
     */
    private String errorDetail;

    /**
     * 创建成功结果
     */
    public static BatchOperationResultDto success(Integer batchId, Integer taskId, 
                                                 String operationType, String message) {
        BatchOperationResultDto result = new BatchOperationResultDto();
        result.setSuccess(true);
        result.setMessage(message);
        result.setBatchId(batchId);
        result.setTaskId(taskId);
        result.setOperationType(operationType);
        result.setOperationTime(LocalDateTime.now());
        return result;
    }

    /**
     * 创建失败结果
     */
    public static BatchOperationResultDto failure(Integer batchId, Integer taskId, 
                                                 String operationType, String message, String errorDetail) {
        BatchOperationResultDto result = new BatchOperationResultDto();
        result.setSuccess(false);
        result.setMessage(message);
        result.setBatchId(batchId);
        result.setTaskId(taskId);
        result.setOperationType(operationType);
        result.setErrorDetail(errorDetail);
        result.setOperationTime(LocalDateTime.now());
        return result;
    }
}
