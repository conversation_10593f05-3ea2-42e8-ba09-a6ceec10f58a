package com.geeksec.task.application.service;

import com.geeksec.task.model.vo.ImportServiceStatusVo;

import java.util.List;

/**
 * 服务状态管理器接口
 * 用于管理多个离线 PCAP 导入探针服务的状态检查和生命周期管理
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface ServiceStatusManager {

    /**
     * 获取所有服务实例的状态
     * 
     * @return 服务状态列表
     */
    List<ImportServiceStatusVo> getAllServiceStatus();

    /**
     * 获取指定服务实例的状态
     * 
     * @param serviceId 服务实例ID
     * @return 服务状态信息，如果服务不存在则返回null
     */
    ImportServiceStatusVo getServiceStatus(Integer serviceId);

    /**
     * 更新服务状态
     * 
     * @param serviceId 服务实例ID
     * @param status 新状态
     * @param taskId 当前任务ID（可为null）
     * @param batchId 当前批次ID（可为null）
     * @return 更新是否成功
     */
    boolean updateServiceStatus(Integer serviceId, ImportServiceStatusVo.ServiceStatus status, 
                               Integer taskId, Integer batchId);

    /**
     * 启动服务实例
     * 
     * @param serviceId 服务实例ID
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @return 启动是否成功
     */
    boolean startService(Integer serviceId, Integer taskId, Integer batchId);

    /**
     * 停止服务实例
     * 
     * @param serviceId 服务实例ID
     * @return 停止是否成功
     */
    boolean stopService(Integer serviceId);

    /**
     * 创建或重启服务实例
     * 
     * @param serviceId 服务实例ID
     * @return 创建是否成功
     */
    boolean createOrRestartService(Integer serviceId);

    /**
     * 更新服务进度
     * 
     * @param serviceId 服务实例ID
     * @param progress 进度 (0.0 - 1.0)
     * @param processedFileCount 已处理文件数
     * @param totalFileCount 总文件数
     * @return 更新是否成功
     */
    boolean updateServiceProgress(Integer serviceId, Double progress, 
                                 Integer processedFileCount, Integer totalFileCount);

    /**
     * 设置服务错误信息
     * 
     * @param serviceId 服务实例ID
     * @param errorMessage 错误信息
     * @return 设置是否成功
     */
    boolean setServiceError(Integer serviceId, String errorMessage);

    /**
     * 检查服务是否健康
     * 
     * @param serviceId 服务实例ID
     * @return 服务是否健康
     */
    boolean isServiceHealthy(Integer serviceId);

    /**
     * 获取空闲服务数量
     * 
     * @return 空闲服务数量
     */
    int getIdleServiceCount();

    /**
     * 获取活跃服务数量
     * 
     * @return 活跃服务数量
     */
    int getActiveServiceCount();
}
