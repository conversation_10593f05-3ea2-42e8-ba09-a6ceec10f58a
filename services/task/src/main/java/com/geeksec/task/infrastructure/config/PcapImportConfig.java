package com.geeksec.task.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * PCAP 导入管理配置类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "nta.task.pcap-import")
public class PcapImportConfig {

    /**
     * 最大并发导入任务数量
     */
    private int maxConcurrentTasks = 3;

    /**
     * 任务调度器配置
     */
    private SchedulerConfig scheduler = new SchedulerConfig();

    /**
     * 规则配置
     */
    private RuleConfig ruleConfig = new RuleConfig();

    /**
     * 任务调度器配置
     */
    @Data
    public static class SchedulerConfig {
        /**
         * 是否启用任务调度器
         */
        private boolean enabled = true;

        /**
         * 任务队列最大容量
         */
        private int maxQueueSize = 100;

        /**
         * 调度间隔（毫秒）
         */
        private long scheduleInterval = 5000L;
    }

    /**
     * 规则配置
     */
    @Data
    public static class RuleConfig {
        /**
         * 规则配置基础路径
         */
        private String basePath = "/tmp/nta/rule-config";
    }
}
