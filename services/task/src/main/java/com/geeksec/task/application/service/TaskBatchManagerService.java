package com.geeksec.task.application.service;

import com.geeksec.task.model.dto.BatchOperationResultDto;
import com.geeksec.task.model.entity.TaskBatch;

/**
 * 任务批次管理服务接口
 * 用于管理任务批次的创建、状态更新、进度跟踪等功能
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface TaskBatchManagerService {

    /**
     * 更新批次PCAP文件数量
     * 
     * @param batchId 批次ID
     * @param pcapCount PCAP文件数量
     * @return 更新是否成功
     */
    boolean updateBatchPcapCount(Integer batchId, Integer pcapCount);

    /**
     * 记录任务批次和离线服务的关联信息
     * 
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @param serviceId 服务实例ID
     * @param serviceName 服务实例名称
     * @return 记录是否成功
     */
    boolean insertBatchOfflineService(Integer taskId, Integer batchId, Integer serviceId, String serviceName);

    /**
     * 更新批次状态
     * 
     * @param batchId 批次ID
     * @param status 新状态 (1: 等待导入, 2: 正在导入, 3: 导入完成, 4: 导入失败)
     * @return 更新是否成功
     */
    boolean updateBatchStatus(Integer batchId, Integer status);

    /**
     * 更新批次进度
     * 
     * @param batchId 批次ID
     * @param progress 进度 (0.0 - 1.0)
     * @return 更新是否成功
     */
    boolean updateBatchProgress(Integer batchId, Double progress);

    /**
     * 更新批次统计信息
     * 
     * @param batchId 批次ID
     * @param batchBytes 导入数据量
     * @param batchSession 导入会话量
     * @param batchAlarm 批次告警量
     * @return 更新是否成功
     */
    boolean updateBatchStatistics(Integer batchId, Long batchBytes, Integer batchSession, Integer batchAlarm);

    /**
     * 获取批次信息
     * 
     * @param batchId 批次ID
     * @return 批次信息
     */
    TaskBatch getBatchInfo(Integer batchId);

    /**
     * 创建新的任务批次
     * 
     * @param taskId 任务ID
     * @param batchRemark 批次描述
     * @param fullflowState 全流量留存状态
     * @param flowlogState 流日志状态
     * @return 创建结果
     */
    BatchOperationResultDto createBatch(Integer taskId, String batchRemark, 
                                       String fullflowState, String flowlogState);

    /**
     * 删除批次
     * 
     * @param batchId 批次ID
     * @return 删除结果
     */
    BatchOperationResultDto deleteBatch(Integer batchId);

    /**
     * 停止批次处理
     * 
     * @param batchId 批次ID
     * @return 停止结果
     */
    BatchOperationResultDto stopBatch(Integer batchId);

    /**
     * 重启批次处理
     * 
     * @param batchId 批次ID
     * @return 重启结果
     */
    BatchOperationResultDto restartBatch(Integer batchId);

    /**
     * 检查批次是否完成
     * 
     * @param batchId 批次ID
     * @return 是否完成
     */
    boolean isBatchCompleted(Integer batchId);

    /**
     * 获取批次处理进度
     * 
     * @param batchId 批次ID
     * @return 进度 (0.0 - 1.0)
     */
    Double getBatchProgress(Integer batchId);

    /**
     * 设置批次错误信息
     * 
     * @param batchId 批次ID
     * @param errorMessage 错误信息
     * @return 设置是否成功
     */
    boolean setBatchError(Integer batchId, String errorMessage);
}
